import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  Text,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {AppButton, AppSvg, FLoading} from 'wini-mobile-components';
import {useDispatch, useSelector} from 'react-redux';

import iconSvg from '../../svg/icon';
import {ColorThemes} from '../../assets/skin/colors';
import ActionBar from '../news/DetailNewsComponent/ActionBar';
import Content from '../news/DetailNewsComponent/Content';
import ListHastTag from '../news/DetailNewsComponent/ListHastTag';

import BasicInfoEvent from './DetailEventComponent/BasicInfoEvent';
import {newsEventAction} from '../../redux/actions/newEventAction';
import {AppDispatch, RootState} from '../../redux/store/store';
import {useNewsEventHook} from '../../redux/reducers/NewsEventReducer';
import {CountdownTimer} from '../news/components/TabEventComponents/components/CountdownTimer';
import {checkTimeStatus} from '../../utils/timeUltis';
import {checkTimeWithNow} from '../../utils/Utils';
import {navigate, RootScreen} from '../../router/router';
import {useForm} from 'react-hook-form';
import {TypoSkin} from '../../assets/skin/typography';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {SocialDA} from '../community/da';
import {TextFieldForm} from '../news/form/component-form';
import {dialogCheckAcc} from '../../Screen/Layout/mainLayout';
import {newsFeedActions} from '../community/reducers/newsFeedReducer';
import {postCommentsActions} from '../community/reducers/postCommentsReducer';
import CommentsListNews from '../customer/listview/commentsNews';

const hashtags = 'Chaivino,Thiennguyen,CongDong,YeuThuong';
const defaultImage = require('../../assets/images/default_img.png');

const DetailEvent = () => {
  const route = useRoute();
  const {id} = route.params as {id: string};
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<'started' | 'onGoing' | 'ended'>(
    'started',
  );

  // Sử dụng Redux store thay vì useState
  const item = useSelector((state: RootState) => state.newsEvent.item);
  const {action} = useNewsEventHook();

  const methods = useForm<any>({shouldFocusError: false});
  const [isLoading, setIsLoading] = useState(false);
  // const [data, setItem] = useState<any>(news);
  const dispatch: AppDispatch = useDispatch();
  const user = useSelectorCustomerState().data;
  const scrollViewRef = useRef<ScrollView>(null);
  const dialogRef = useRef<any>(null);
  const [isRefresh, setIsRefresh] = useState(false);
  const socialDA = new SocialDA();

  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
    if (item) {
      const status = checkTimeStatus(item.DateStart, item.DateEnd);
      setStatus(status || 'started');
    }
  }, [item]);

  const initData = async () => {
    try {
      setLoading(true);
      const res = await newsEventAction.fetchById(id);
      if (res) {
        // Cập nhật item trong Redux store
        // get cmts
        const comments = await socialDA.getComment(res?.Id, false, true);
        // get likes
        const likes = await socialDA.getLikes(res?.Id, false, true);
        const data = {
          ...res,
          Comment: comments?.data?.length ?? 0,
          Likes: likes?.data ?? [],
          LikesCount: likes?.data?.length ?? 0,
        };
        action.setData({
          stateName: 'item',
          data: data,
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const navigateToHashtagScreen = (hashtag: string) => {
    navigate(RootScreen.ListByHashtagScreen, {
      hashtag,
      type: 'event',
    });
  };

  const handleToggleLike = useCallback(
    async (isCurrentlyLiked: boolean) => {
      if (!user || !item?.Id) {
        return;
      }

      const newLikeState = !isCurrentlyLiked;

      const data = {
        ...item,
        IsLike: newLikeState,
        Likes: isCurrentlyLiked
          ? item.Likes.filter((like: any) => like.CustomerId !== user.Id)
          : [...item.Likes, {CustomerId: user.Id}],
        LikesCount: isCurrentlyLiked
          ? Math.max(0, (item.LikesCount || 0) - 1)
          : (item.LikesCount || 0) + 1,
      };
      action.setData({
        stateName: 'item',
        data: data,
      });

      try {
        // Update server
        await dispatch(
          newsFeedActions.updateLike(item.Id, isCurrentlyLiked, false, true),
        );
      } catch (error) {
        console.error('Failed to update like:', error);
        // Revert optimistic update on error
        const data = {
          ...item,
          IsLike: isCurrentlyLiked,
          LikesCount: isCurrentlyLiked
            ? (item.LikesCount || 0) + 1
            : Math.max(0, (item.LikesCount || 0) - 1),
        };
        action.setData({
          stateName: 'item',
          data: data,
        });
      }
    },
    [dispatch, item, user],
  );

  const handleReply = useCallback(
    async (dataComment?: any) => {
      if (dataComment) {
        methods.setValue('Comment', undefined);
        methods.setValue('Comment', `@${dataComment.relativeUser.title} `);
        methods.setValue('CommentId', `${dataComment.Id}`);
        methods.setValue('UserComment', `${dataComment.relativeUser.title}`);
        // Focus the comment input field
      } else {
        methods.setValue('Comment', undefined);
        methods.setValue('CommentId', undefined);
        methods.setValue('UserComment', undefined);
      }
    },
    [methods],
  );

  const handleAddComment = async () => {
    if (user) {
      if (methods.getValues().Comment) {
        if (item)
          await dispatch(
            postCommentsActions.addNewComment(
              item?.Id,
              methods.getValues().Comment,
              methods.getValues().CommentId?.toString().trim() || undefined,
              false,
              true,
            ),
          );

        action.setData({
          stateName: 'item',
          data: {
            ...item,
            Comment: item.Comment + 1,
          },
        });

        if (
          methods.getValues().CommentId === undefined ||
          methods.getValues().CommentId === ''
        ) {
          scrollViewRef.current?.scrollToEnd({
            animated: true,
          });
        }

        methods.setValue('Comment', '');
        methods.setValue('CommentId', '');
        methods.setValue('UserComment', '');

        // dispatch(newsFeedActions.updateCommentCount(data?.Id, 1));
        // const newfeed = store
        //   .getState()
        //   .newsFeed.data.find((item: any) => item.Id === data?.Id);
        // if (newfeed) {
        //   dispatch(newsFeedActions.updateCommentCount(newfeed.Id, 1));
        // }
        if (item)
          dispatch(postCommentsActions.loadComments(item?.Id, false, true));
      }
    } else {
      ///TODO: check chưa login thì confirm ra trang login
      dialogCheckAcc(dialogRef);
    }
  };

  if (!item) return null;

  const renderEventImage = () => {
    const imageSource = item.Img ? {uri: item.Img} : defaultImage;

    if (checkTimeWithNow(item.DateStart)) {
      return (
        <ImageBackground source={imageSource} style={styles.cardImage}>
          <CountdownTimer TargetDate={item.DateStart} textSize="large" />
        </ImageBackground>
      );
    }

    return <Image source={imageSource} style={styles.cardImage} />;
  };

  return (
    <SafeAreaView style={styles.container}>
      <FLoading visible={loading} avt={require('../../assets/appstore.png')} />
      {/* Button back luôn hiển thị ở góc */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}>
        <AppSvg SvgSrc={iconSvg.arrowLeft} size={20} />
      </TouchableOpacity>

      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        {/* Main Image */}
        <View>{renderEventImage()}</View>

        <View style={{paddingHorizontal: 16}}>
          {/* các thao tác */}
          {item && (
            <ActionBar handleToggleLike={handleToggleLike} data={item} />
          )}

          {/* thông tin cơ bản */}
          {item && <BasicInfoEvent event={item} />}

          {/* nội dung */}
          {item && <Content data={item?.Content} />}

          {/* hashtag */}
          {item && (
            <ListHastTag
              hashtags={item?.Hashtag || ''}
              onPress={navigateToHashtagScreen}
            />
          )}

          {/* bình luận */}
          {/* {item && <ListComment />} */}
          {item && (
            <CommentsListNews
              postId={item?.Id}
              isNew={false}
              isEvent={true}
              onReply={handleReply}
            />
          )}
        </View>
      </ScrollView>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        <View
          style={{
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
            paddingHorizontal: 16,
            marginBottom: 32,
            paddingTop: 8,
            borderTopWidth: 1,
            borderTopColor: ColorThemes.light.neutral_main_border_color,
          }}>
          {methods.watch('CommentId') ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 4,
              }}>
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>
                Trả lời {methods.getValues().UserComment ?? ''}
              </Text>
              <Text
                onPress={() => {
                  methods.setValue('CommentId', undefined);
                  methods.setValue('Comment', undefined);
                  methods.setValue('UserComment', undefined);
                }}
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>
                - Hủy
              </Text>
            </View>
          ) : null}
          <View
            style={{
              flexDirection: 'row',
              gap: 8,
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TextFieldForm
              textFieldStyle={{
                padding: 16,
                height: 40,
                paddingVertical: 0,
              }}
              style={{
                flex: 1,
              }}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              placeholder="Viết bình luận của bạn"
              name="Comment"
            />
            <AppButton
              prefixIcon={'fill/user interface/send-message'}
              prefixIconSize={24}
              backgroundColor={ColorThemes.light.transparent}
              borderColor="transparent"
              containerStyle={{
                paddingHorizontal: 12,
                height: 45,
              }}
              onPress={handleAddComment}
              textColor={ColorThemes.light.primary_main_color}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
  mainImage: {
    width: '100%',
    height: 400,
    resizeMode: 'cover',
  },
  backButton: {
    position: 'absolute',
    top: 65,
    left: 16,
    backgroundColor: ColorThemes.light.secondary1_sub_color,
    width: 35,
    height: 35,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    elevation: 5, // cho Android
  },
  cardImage: {
    width: '100%',
    height: 400,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderRadius: 8,
  },
});

export default DetailEvent;
