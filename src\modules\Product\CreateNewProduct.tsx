/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from 'react-native';
import {
  AppButton,
  FBottomSheet,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import { useNavigation, useRoute } from '@react-navigation/native';

import HeaderShop from '../../components/shop/HeaderShop';

import CreateProduct from '../../components/Product/CreateProduct';
import { InforHeader } from '../../Screen/Layout/headers/inforHeader';

const CreateNewProduct = () => {
  const route = useRoute<any>();
  const [title, setTitle] = useState<string>('');

  useEffect(() => {
    if (route?.params?.title) {
      setTitle(route.params.title);
    }
  }, [route.params.title]);

  return (
    <View style={styles.container}>
      <InforHeader title={title} />
      <CreateProduct routeParams={route.params} />
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },

  navigator: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#00FFFF',
    paddingBottom: 18,
    borderBottomWidth: 0.5,
  },
});

export default CreateNewProduct;
