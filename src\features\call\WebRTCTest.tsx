import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import WebRTCService from './WebRTCService';
import { mediaDevices } from 'react-native-webrtc';

// Component test để kiểm tra WebRTC functionality
const WebRTCTest: React.FC = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [callState, setCallState] = useState(WebRTCService.getCallState());

  useEffect(() => {
    // Test permissions
    checkPermissions();
    
    // Setup WebRTC callbacks
    WebRTCService.setCallbacks({
      onCallStateChanged: (state) => {
        setCallState(state);
        console.log('Call state changed:', state);
      },
      onError: (error) => {
        console.error('WebRTC Error:', error);
        Alert.alert('WebRTC Error', error.message || 'Unknown error');
      },
      onLocalStream: (stream) => {
        console.log('Local stream received:', stream);
      },
      onRemoteStream: (stream) => {
        console.log('Remote stream received:', stream);
      },
    });

    // Setup socket listeners
    WebRTCService.setupSocketListeners();
  }, []);

  const checkPermissions = async () => {
    try {
      const stream = await mediaDevices.getUserMedia({ audio: true, video: false });
      setHasPermission(true);
      console.log('✅ Microphone permission granted');
      
      // Stop the test stream
      stream.getTracks().forEach(track => track.stop());
    } catch (error: any) {
      setHasPermission(false);
      console.error('❌ Microphone permission denied:', error);
      Alert.alert(
        'Permission Required',
        'Microphone permission is required for audio calls. Please enable it in settings.',
        [{ text: 'OK' }]
      );
    }
  };

  const testCall = async () => {
    try {
      console.log('Testing call functionality...');
      await WebRTCService.startCall('test-user-id', 'Test User');
      Alert.alert('Success', 'Call test started successfully');
    } catch (error: any) {
      console.error('Call test failed:', error);
      Alert.alert('Call Test Failed', error.message || 'Unknown error');
    }
  };

  const endCall = () => {
    WebRTCService.endCall();
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>WebRTC Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Microphone Permission: {hasPermission ? '✅ Granted' : '❌ Denied'}
        </Text>
        <Text style={styles.statusText}>
          Socket Connected: {isConnected ? '✅ Connected' : '❌ Disconnected'}
        </Text>
        <Text style={styles.statusText}>
          In Call: {callState.isInCall ? '✅ Yes' : '❌ No'}
        </Text>
        {callState.isInCall && (
          <Text style={styles.statusText}>
            Call Type: {callState.isIncoming ? 'Incoming' : 'Outgoing'}
          </Text>
        )}
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.testButton]}
          onPress={testCall}
          disabled={!hasPermission}
        >
          <Text style={styles.buttonText}>Test Call</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.endButton]}
          onPress={endCall}
          disabled={!callState.isInCall}
        >
          <Text style={styles.buttonText}>End Call</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.permissionButton]}
          onPress={checkPermissions}
        >
          <Text style={styles.buttonText}>Check Permissions</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.debugContainer}>
        <Text style={styles.debugTitle}>Debug Info:</Text>
        <Text style={styles.debugText}>
          {JSON.stringify(callState, null, 2)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  statusContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  statusText: {
    fontSize: 16,
    marginBottom: 5,
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  testButton: {
    backgroundColor: '#4CAF50',
  },
  endButton: {
    backgroundColor: '#F44336',
  },
  permissionButton: {
    backgroundColor: '#2196F3',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  debugContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    flex: 1,
  },
  debugTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  debugText: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
});

export default WebRTCTest;
