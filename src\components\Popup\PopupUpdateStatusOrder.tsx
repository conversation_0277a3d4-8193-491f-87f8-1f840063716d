import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  TextInput,
} from 'react-native';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';

const PopupUpdateStatusOrder = ({
  visible,
  onClose,
  item,
  handleUpdateStatusProcessOrder,
}: {
  visible: boolean;
  onClose: () => void;
  item: any;
  handleUpdateStatusProcessOrder: (item: any, type?: string) => Promise<void>;
}) => {
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [cancelReason, setCancelReason] = useState('');

  // Reset state when modal is closed
  useEffect(() => {
    if (!visible) {
      setSelectedStatus(null);
      setCancelReason('');
    }
  }, [visible]);

  const statusOptions = [
    {id: 1, name: '<PERSON><PERSON><PERSON> n<PERSON>ận đơn hàng', type: 'processing'},
    {id: 3, name: '<PERSON><PERSON><PERSON> thành', type: 'completed'},
    {id: 4, name: '<PERSON><PERSON><PERSON>', type: 'cancelled'},
  ];

  const getFilteredStatusOptions = () => {
    if (item?.Status === 1) {
      return statusOptions.filter(
        status => status.type === 'processing' || status.type === 'cancelled',
      );
    } else if (item?.Status === 2) {
      return statusOptions.filter(
        status =>
          // status.type === 'Pending' ||
          // status.type === 'Delivery' ||
          status.type === 'completed' || status.type === 'cancelled',
      );
    }
    return statusOptions;
  };

  const handleStatusSelect = (statusType: string) => {
    setSelectedStatus(statusType);
    if (statusType !== 'cancelled') {
      setCancelReason('');
    }
  };

  const handleUpdateStatus = async () => {
    if (selectedStatus) {
      if (selectedStatus === 'cancelled' && !cancelReason.trim()) {
        return;
      }

      const itemWithCancelReason =
        selectedStatus === 'cancelled'
          ? {...item, cancelReason: cancelReason.trim()}
          : item;

      await handleUpdateStatusProcessOrder(
        itemWithCancelReason,
        selectedStatus,
      );
      onClose(); // Close modal on success
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              Cập nhật đơn hàng #
              <Text style={{fontWeight: 'bold'}}>{item?.Code}</Text>
            </Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
          </View>

          {/* Order Info */}
          <View style={styles.orderInfoContainer}></View>
          <View style={styles.statusList}>
            {getFilteredStatusOptions().map(status => (
              <TouchableOpacity
                key={status.id}
                style={[
                  styles.statusOption,
                  selectedStatus === status.type && styles.selectedStatus,
                ]}
                onPress={() => handleStatusSelect(status.type)}>
                <View style={styles.statusOptionContent}>
                  <View
                    style={[
                      styles.statusIndicator,
                      selectedStatus === status.type &&
                        styles.selectedIndicator,
                    ]}
                  />
                  <Text
                    style={[
                      styles.statusOptionText,
                      selectedStatus === status.type &&
                        styles.selectedStatusText,
                    ]}>
                    {status.name}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}

            {/* Cancel Reason Input */}
            {selectedStatus === 'cancelled' && (
              <View style={styles.cancelReasonContainer}>
                <Text style={styles.cancelReasonLabel}>
                  Lý do hủy đơn hàng *
                </Text>
                <TextInput
                  style={styles.cancelReasonInput}
                  placeholder="Nhập lý do hủy đơn hàng..."
                  value={cancelReason}
                  onChangeText={setCancelReason}
                  multiline={true}
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtonsContainer}>
            <View style={styles.additionalButtons}>
              <TouchableOpacity
                style={[
                  styles.additionalButton,
                  (!selectedStatus ||
                    (selectedStatus === 'cancelled' && !cancelReason.trim())) &&
                    styles.disabledButton,
                ]}
                onPress={handleUpdateStatus}
                disabled={
                  !selectedStatus ||
                  (selectedStatus === 'cancelled' && !cancelReason.trim())
                }>
                <Text
                  style={[
                    styles.additionalButtonText,
                    (!selectedStatus ||
                      (selectedStatus === 'cancelled' &&
                        !cancelReason.trim())) &&
                      styles.disabledButtonText,
                  ]}>
                  Cập nhật trạng thái
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.65)',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: '#ffffff',
    padding: 24,
    borderRadius: 20,
    width: '100%',
    maxWidth: 420,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 20},
    shadowOpacity: 0.35,
    shadowRadius: 35,
    elevation: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalTitle: {
    ...TypoSkin.title2,
    fontWeight: '700',
    color: '#1a1a1a',
    flex: 1,
    fontSize: 18,
    lineHeight: 24,
  },
  modalMessage: {
    ...TypoSkin.title4,
    color: '#666666',
    marginTop: 8,
    lineHeight: 22,
    fontSize: 14,
  },
  statusList: {
    width: '100%',
    marginBottom: 28,
    marginTop: 20,
  },
  statusOption: {
    paddingVertical: 18,
    paddingHorizontal: 20,
    borderWidth: 2,
    borderColor: '#f0f0f0',
    borderRadius: 16,
    marginBottom: 12,
    backgroundColor: '#fafbfc',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    position: 'relative',
    overflow: 'hidden',
  },
  selectedStatus: {
    backgroundColor: '#fff8e1',
    borderColor: '#ffc107',
    borderWidth: 2,
    shadowColor: '#ffc107',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
    transform: [{scale: 1.02}],
  },
  statusOptionText: {
    ...TypoSkin.title3,
    color: '#333333',
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 20,
  },
  selectedStatusText: {
    color: '#e65100',
    fontWeight: '700',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 12,
  },
  modalButton: {
    flex: 1,
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  cancelButtonText: {
    ...TypoSkin.title3,
    color: '#666666',
    fontWeight: '600',
    fontSize: 16,
  },
  confirmModalButton: {
    backgroundColor: '#ff6b35',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#ff6b35',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  confirmModalButtonText: {
    ...TypoSkin.title3,
    color: '#ffffff',
    fontWeight: '700',
    fontSize: 16,
  },
  additionalButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  additionalButton: {
    backgroundColor: '#ffc107',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 14,
    alignItems: 'center',
    flex: 1,
    shadowColor: '#ffc107',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 193, 7, 0.3)',
  },
  additionalButtonText: {
    ...TypoSkin.title3,
    color: '#b71800',
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 8,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  closeButtonText: {
    fontSize: 22,
    color: '#6c757d',
    fontWeight: '400',
    lineHeight: 22,
    textAlign: 'center',
  },
  orderInfoContainer: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  orderInfoText: {
    ...TypoSkin.title2,
    fontWeight: '700',
    color: '#1a1a1a',
    fontSize: 16,
  },
  sectionTitle: {
    ...TypoSkin.title2,
    fontWeight: '700',
    color: '#1a1a1a',
    marginBottom: 16,
    fontSize: 16,
  },
  statusOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 16,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    backgroundColor: 'transparent',
  },
  selectedIndicator: {
    backgroundColor: '#ffc107',
    borderColor: '#ffc107',
    shadowColor: '#ffc107',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  actionButtonsContainer: {
    marginTop: 8,
  },
  disabledButton: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
    shadowOpacity: 0,
    elevation: 0,
  },
  disabledButtonText: {
    color: '#999999',
    fontWeight: '500',
  },
  cancelReasonContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cancelReasonLabel: {
    ...TypoSkin.title3,
    color: '#333333',
    fontWeight: '600',
    fontSize: 14,
    marginBottom: 8,
  },
  cancelReasonInput: {
    ...TypoSkin.title4,
    color: '#333333',
    padding: 12,
    borderWidth: 1,
    borderColor: '#ced4da',
    borderRadius: 8,
    backgroundColor: '#ffffff',
    minHeight: 80,
    textAlignVertical: 'top',
  },
});

export default PopupUpdateStatusOrder;
