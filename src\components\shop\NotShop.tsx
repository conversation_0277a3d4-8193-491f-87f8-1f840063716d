/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Animated,
  Dimensions,
  Linking,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  Text,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { RootScreen } from '../../router/router';
import { TypoSkin } from '../../assets/skin/typography';
import { ColorThemes } from '../../assets/skin/colors';

const NotShop = () => {
  const navigation = useNavigation<any>();

  const handleRegisterShop = () => {
    navigation.navigate(RootScreen.RegisterShop);
  };
  return (
    <View style={styles.content}>
      <Image
        source={require('../../assets/images/registerShop.png')}
        style={styles.illustration}
      />
      <Text style={styles.description}>
        <PERSON><PERSON><PERSON> là kh<PERSON>ch hàng c<PERSON> nhân, <PERSON><PERSON><PERSON>{'\n'}
        <Text style={styles.boldText}>“<PERSON><PERSON><PERSON> ký”</Text> để chuyển đổi thành cửa
        hàng
      </Text>
      <TouchableOpacity
        style={styles.buyButton}
        onPress={() => handleRegisterShop()}>
        <Text style={styles.actionButtonText}>Đăng Ký</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
  },
  illustration: {
    width: 231,
    height: 203.54,
  },
  description: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    marginTop: 50,
  },
  boldText: {
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  button: {
    backgroundColor: '#2196F3',
    borderRadius: 25,
    paddingVertical: 15,
    marginHorizontal: 20,
    marginBottom: 30,
    alignItems: 'center',
  },
  buttonText: {
    ...TypoSkin.title3,
    fontWeight: 'bold',
  },
  buyButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    width: 325,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 25,
    height: 50,
    borderRadius: 30,
    marginBottom: 30,
  },

  actionButtonText: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 17,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default NotShop;
