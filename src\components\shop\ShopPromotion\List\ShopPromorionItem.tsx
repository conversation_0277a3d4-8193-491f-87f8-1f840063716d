import React, {use, useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  Image,
} from 'react-native';
import {Checkbox, ListTile, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faAngleRight} from '@fortawesome/free-solid-svg-icons';
import {FlatList} from 'react-native';
import EmptyPage from '../../../../Screen/emptyPage';

const ItemPromorionCard = (
  data: any,
  selectedDataOne: string,
  setSelectedDataOne: (data: string) => void,
) => {
  if (data?.data?.length < 1) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <EmptyPage />
      </View>
    );
  }
  return (
    <View style={{height: '100%', width: '100%'}}>
      {data?.data?.map((item: any) => (
        <View style={styles.itemContainer}>
          <View style={styles.itemText}>
            <Checkbox value={true} onChange={() => {}} size={24} />
            <Text style={{marginLeft: 10, fontSize: 20, fontFamily: 'roboto'}}>
              {item?.Name}
            </Text>
          </View>
          <TouchableOpacity style={{marginRight: 10}}>
            <FontAwesomeIcon
              icon={faAngleRight}
              color={ColorThemes.light.black}
              size={16}
            />
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 65,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    marginLeft: 20,
  },
  itemText: {
    fontSize: 20,
    color: 'black',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',

    marginLeft: 20,
    marginRight: 10,
  },
});

export default ItemPromorionCard;
