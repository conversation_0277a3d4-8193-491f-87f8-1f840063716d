import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  Image,
} from 'react-native';
import {FlatList} from 'react-native-gesture-handler';
import {SelectItemProduct} from '../../mock/shopData';
import ConfigAPI from '../../Config/ConfigAPI';
import {Radio, RadioAction} from '../Field/Radio';

interface ListItemProps {
  setSelecChildID: (item: any) => void;
  setSelecChildName: (item: any) => void;
  dataLabel: any[];
}

const ListItemLable = (props: ListItemProps) => {
  const {setSelecChildID, setSelecChildName, dataLabel} = props;

  const [isSelected, setIsSelected] = useState('');
  const [data, setData] = useState<any[]>();
  useEffect(() => {
    if (dataLabel && dataLabel?.length > 0) {
      setData(dataLabel);
    }
  }, [dataLabel]);

  const handleSelectLabel = (item: any) => {
    setIsSelected(item?.Id);
    setSelecChildID(item?.Id);
    setSelecChildName(item?.Name);
  };

  return (
    <FlatList
      data={data}
      style={{flex: 1, marginBottom: 50}}
      // contentContainerStyle={{ paddingBottom: 100 }}
      keyExtractor={(item, i) => `${i} ${item.Id}`}
      renderItem={({item, index}) => (
        <Pressable style={styles.itemContainer}>
          <View style={styles.itemText}>
            <Image
              source={{
                uri: item.Img,
              }}
              style={{width: 30, height: 30, borderRadius: 100}}
            />
            <TouchableOpacity onPress={() => handleSelectLabel(item)}>
              <Text style={{marginLeft: 10, fontSize: 20}}>{item.Name}</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity onPress={() => handleSelectLabel(item)}>
            {isSelected == item.Id ? <RadioAction /> : <Radio />}
          </TouchableOpacity>
        </Pressable>
      )}
    />
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  itemText: {
    color: 'black',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  radio: {
    width: 30,
    height: 30,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  radioSelected: {
    backgroundColor: '#007AFF',
    padding: 5,
  },
});

export default ListItemLable;
