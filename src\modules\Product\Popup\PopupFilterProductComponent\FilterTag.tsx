import React from 'react';
import {Pressable, Text, StyleSheet, View} from 'react-native';

interface FilterTagProps {
  label: string;
  isActive: boolean;
  onPress: () => void;
  type: 'hot' | 'freeShip' | 'new';
  backgroundColor: string;
}

const FilterTag = ({
  label,
  isActive,
  onPress,
  backgroundColor,
}: FilterTagProps) => {
  return (
    <Pressable
      style={[
        styles.filterTag,
        isActive && styles.filterTagActive,
        {backgroundColor},
      ]}
      onPress={onPress}>
      <Text style={styles.filterTagText}>{label}</Text>
      {isActive && (
        <View style={styles.checkIcon}>
          <Text style={styles.checkIconText}>✓</Text>
        </View>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  filterTag: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  filterTagActive: {
    // Common active style if any, otherwise specific in type
  },
  filterTagText: {
    fontSize: 14,
    color: 'white',
    fontWeight: 'bold',
  },
  checkIcon: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#0052FF',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  checkIconText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default FilterTag;
