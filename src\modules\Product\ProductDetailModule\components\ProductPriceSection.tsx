import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ProductPriceSectionProps} from '../types';
import {TypoSkin} from '../../../../assets/skin/typography';
import {Ultis} from '../../../../utils/Utils';

const ProductPriceSection: React.FC<ProductPriceSectionProps> = ({
  price = 0,
  discount,
  isFavorite,
  onFavoritePress,
  onSharePress,
}) => {
  const discountedPrice =
    discount && discount > 0 ? price - (price * discount) / 100 : price;

  return (
    <View style={styles.priceContainer}>
      {discount && discount > 0 ? (
        <View style={styles.priceInfo}>
          <Text style={styles.priceText}>{Ultis.money(discountedPrice)}đ</Text>
          <Text style={styles.originalPrice}>{Ultis.money(price)} đ</Text>
          <View style={styles.discountBadge}>
            <Text style={styles.discountText}>{discount}%</Text>
          </View>
        </View>
      ) : (
        <Text style={styles.priceText}>{Ultis.money(price)} đ</Text>
      )}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={onFavoritePress}>
          <Winicon
            src={
              isFavorite
                ? 'fill/user interface/heart'
                : 'outline/user interface/heart'
            }
            size={20}
            color={isFavorite ? '#FF0000' : '#000'}
          />
        </TouchableOpacity>
        <TouchableOpacity style={styles.shareButton} onPress={onSharePress}>
          <Winicon src="fill/user interface/share" size={20} color="#000" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 0.3,
    borderBottomColor: '#90C8FB',
    marginTop: 12,
  },
  priceText: {
    ...TypoSkin.title2,
    color: '#FF4D4F',
  },
  originalPrice: {
    ...TypoSkin.title5,
    color: '#999',
    textDecorationLine: 'line-through',
  },
  discountBadge: {
    backgroundColor: '#FFA500',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  discountText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  favoriteButton: {},
  shareButton: {},
  priceInfo: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
});

export default ProductPriceSection;
