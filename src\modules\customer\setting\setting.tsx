import {ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useEffect, useRef, useState} from 'react';
import {useDispatch} from 'react-redux';
import {useForm} from 'react-hook-form';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {
  AppButton,
  ComponentStatus,
  FDialog,
  FLoading,
  showDialog,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import ScreenHeader from '../../../Screen/Layout/header';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import WScreenFooter from '../../../Screen/Layout/footer';
import {RootScreen} from '../../../router/router';
import EditCustomer from '../form/edit-customer';
import {Ultis} from '../../../utils/Utils';
import {useTranslation} from 'react-i18next';
import HeaderBackground from '../../../components/shop/HeaderShop';
import {DataController} from '../../../base/baseController';

export default function SettingProfile() {
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const userData = useSelectorCustomerState().data;
  const [onLoading, setOnLoading] = useState(false);
  const dispatch = useDispatch<any>();
  const methods = useForm({shouldFocusError: false});
  const [onEdit, setOnEdit] = useState(false);
  const customerController = new DataController('Customer');

  useEffect(() => {
    const data: any = userData ?? {};
    Object.keys(data).forEach(props => {
      if (data[props]) {
        if (props === 'Gender') {
          methods.setValue(props, data[props] ? 1 : 0);
        } else {
          methods.setValue(props, data[props]);
        }
      }
    });
  }, []);

  const submitEdit = async () => {
    const newData = methods.getValues();
    if (newData.gender) {
      newData.gender = newData.gender === 1;
    }

    //lấy thông tin theo ref code
    var parentId;
    var listParent;
    if (methods.watch('RefCode')?.length > 0) {
      const resRef = await customerController.getListSimple({
        page: 1,
        size: 1,
        query: `@RefCode: (*${methods.watch('RefCode')}*)`,
        returns: ['Id', 'Name', 'AvatarUrl', 'ListParent'],
      });
      if (resRef.code === 200 && resRef.data.length > 0) {
        parentId = resRef.data[0].Id;
        listParent = resRef.data[0].ListParent
          ? resRef.data[0].ListParent + ',' + resRef.data[0].Id
          : resRef.data[0].Id;
      } else {
        showSnackbar({
          message: 'Mã giới thiệu không chính xác, thử lại sau.',
          status: ComponentStatus.ERROR,
        });
        return;
      }
    }
    dispatch(
      CustomerActions.edit({
        ...newData,
        RanksData: undefined,
        ParentId: parentId,
        ListParent: listParent,
      }),
    ).then(() => {
      setOnEdit(false);
      dispatch(CustomerActions.getInfor());
    });
  };

  const dialogDelAccRef = useRef<any>(null);
  const dialogDelAcc = () => {
    showDialog({
      ref: dialogDelAccRef,
      status: ComponentStatus.WARNING,
      title: t('profile.deleteAccountConfirm'),
      content: t('profile.deleteAccountWarning'),
      onSubmit: async () => {
        if (userData?.Id) {
          setOnLoading(true);
          await CustomerActions.delete(dispatch, userData.Id, navigation).then(
            () => {
              setOnLoading(false);
            },
          );
        }
      },
    });
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FLoading
        visible={onLoading}
        avt={require('../../../assets/appstore.png')}
      />
      <FDialog ref={dialogDelAccRef} />
      <HeaderBackground />
      <ScreenHeader
        title={onEdit ? t('profile.editInfo') : t('profile.title')}
        onBack={() => navigation.goBack()}
        action={
          !onEdit ? (
            <TouchableOpacity
              style={{padding: 16}}
              onPress={() => {
                setOnEdit(true);
              }}>
              <Winicon
                src="outline/users/user-edit"
                color={ColorThemes.light.neutral_text_subtitle_color}
                size={20}
              />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={{padding: 16}}
              onPress={() => {
                setOnEdit(false);
              }}>
              <Text
                style={{
                  ...TypoSkin.buttonText4,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>
                {t('common.cancel')}
              </Text>
            </TouchableOpacity>
          )
        }
      />
      <ScrollView>
        {onEdit ? (
          <View style={{flex: 1}}>
            <EditCustomer methods={methods} />
          </View>
        ) : (
          <View style={{backgroundColor: '#fff'}}>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.name')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Name ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.email')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Email ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.phone')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Mobile ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.birthdate')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {Ultis.datetoString(new Date(userData?.Dob ?? 0))}
              </Text>
            </View>

            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.gender')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Gender == 1
                  ? t('profile.male')
                  : t('profile.female')}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.address')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Address ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>
                {t('profile.accountCreationDate')}
              </Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.neutral_text_title_color,
                    textAlign: 'right',
                  },
                ]}>
                {Ultis.formatDateTime(userData?.DateCreated, false)}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
      {onEdit ? null : (
        <WScreenFooter style={{flexDirection: 'row', alignItems: 'center'}}>
          <AppButton
            containerStyle={{
              borderRadius: 8,
              marginHorizontal: 16,
              marginBottom: 65,
              flex: 1,
              backgroundColor: ColorThemes.light.primary_main_color,
              justifyContent: 'center',
            }}
            borderColor="transparent"
            title={
              !userData?.Password
                ? t('profile.createPassword')
                : t('profile.updatePassword')
            }
            onPress={() => {
              navigation.push(RootScreen.ForgotPass);
            }}
          />
        </WScreenFooter>
      )}
      <WScreenFooter style={{flexDirection: 'row', alignItems: 'center'}}>
        <AppButton
          containerStyle={{
            borderRadius: 8,
            marginHorizontal: 16,
            flex: 1,
            backgroundColor: onEdit
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.error_main_color,
            justifyContent: 'center',
          }}
          borderColor="transparent"
          title={onEdit ? t('profile.saveChanges') : t('profile.deleteAccount')}
          onPress={onEdit ? submitEdit : dialogDelAcc}
        />
      </WScreenFooter>
    </View>
  );
}
