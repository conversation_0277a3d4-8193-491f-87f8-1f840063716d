import React, {useEffect, useCallback, memo, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
  Image,
  Text,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useSelector} from 'react-redux';

import {AppSvg, showDialog, ComponentStatus} from 'wini-mobile-components';
import {navigate, RootScreen} from '../../../router/router';
import CartIcon from '../../../components/CartIcon';
import iconSvg from '../../../svg/icon';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import HeaderBackground from '../../../components/shop/HeaderShop';
import {useCategoryHook} from '../../../redux/hook/categoryHook';
import {ColorThemes} from '../../../assets/skin/colors';
import SearchBar from '../../../components/shop/Search';
import {RootState} from '../../../redux/store/store';
import {useProductByCategoryHook} from '../../../redux/reducers/ProductByCategoryReducer';
import PopupFilterProduct from '../../../modules/Product/Popup/PopupFilterProduct';
import {categoryAction} from '../../../redux/actions/categoryAction';

// Types
interface HeaderIconButtonProps {
  onPress?: () => void;
  children: React.ReactNode;
}

interface RightHeaderActionsProps {
  onNotificationPress: () => void;
  onCartPress: () => void;
}

interface FilterData {
  categoryId?: string | null;
  textSearch?: string;
  [key: string]: any;
}

// Header Icon Button Component
const HeaderIconButton = memo<HeaderIconButtonProps>(({onPress, children}) => (
  <TouchableOpacity style={styles.iconButton} onPress={onPress}>
    <View style={styles.iconCircle}>{children}</View>
  </TouchableOpacity>
));

// Right Header Actions Component
const RightHeaderActions = memo<RightHeaderActionsProps>(
  ({onNotificationPress, onCartPress}) => (
    <View style={styles.rightIcons}>
      <Image
        source={require('../../../assets/images/logo.png')}
        style={styles.logoImage}
      />
      <HeaderIconButton onPress={onNotificationPress}>
        <AppSvg SvgSrc={iconSvg.notification} size={16} />
      </HeaderIconButton>
      <HeaderIconButton onPress={onCartPress}>
        <CartIcon isHome color="#0033CC" size={18} showBadge={true} />
      </HeaderIconButton>
    </View>
  ),
);

const CategoryHeader: React.FC = () => {
  const navigation = useNavigation();
  const categoryHook = useCategoryHook();
  const productByCategoryHook = useProductByCategoryHook();
  const customer = useSelectorCustomerState().data;
  const dialogRef = useRef<any>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [showFilter, setShowFilter] = useState<boolean>(false);

  const {parentCategory, filter} = useSelector(
    (state: RootState) => state.productByCategory,
  );

  // Setup status bar
  useEffect(() => {
    if (Platform.OS === 'android') {
      StatusBar.setTranslucent(true);
      StatusBar.setBackgroundColor('transparent');
    }
    StatusBar.setBarStyle('dark-content');

    return () => {
      if (Platform.OS === 'android') {
        StatusBar.setTranslucent(false);
        StatusBar.setBackgroundColor('transparent');
      }
    };
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Navigation handlers
  const onBack = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  // Authentication check
  const handleProtectedAction = useCallback(
    (action: () => void) => {
      if (!customer?.Id) {
        showDialog({
          ref: dialogRef,
          status: ComponentStatus.WARNING,
          title: 'Vui lòng đăng nhập để sử dụng!',
          onSubmit: async () => {
            navigate(RootScreen.login);
          },
        });
        return;
      }
      action();
    },
    [customer?.Id],
  );

  // Search handler
  const onSearch = useCallback(
    (text: string) => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
        productByCategoryHook.setData('filter', {
          ...filter,
          textSearch: text,
        });
      }, 1000);
    },
    [filter, productByCategoryHook],
  );

  // Filter application
  const onApplyFilter = useCallback(
    async (data: FilterData) => {
      let updatedData = {...data};

      if (data.categoryId) {
        try {
          const childrenCategory = await categoryAction.find({
            searchRaw: `@ParentId:{${data.categoryId}}`,
          });

          if (childrenCategory.length > 0) {
            updatedData.categoryId +=
              '|' +
              childrenCategory.map((category: any) => category.Id).join('|');
          }
        } catch (error) {
          console.error('Error fetching child categories:', error);
        }
      }

      productByCategoryHook.setData('filter', {
        ...filter,
        ...updatedData,
      });

      if (updatedData.categoryId) {
        productByCategoryHook.setData('newCategoryId', updatedData.categoryId);
      }
    },
    [filter, productByCategoryHook],
  );

  // Action handlers
  const handleNotificationPress = useCallback(() => {
    handleProtectedAction(() => {
      navigate(RootScreen.Notification);
    });
  }, [handleProtectedAction]);

  const handleCartPress = useCallback(() => {
    handleProtectedAction(() => {
      navigate(RootScreen.CartPage);
    });
  }, [handleProtectedAction]);

  const onShowFilter = useCallback(() => {
    setShowFilter(true);
  }, []);

  const onCloseFilter = useCallback(() => {
    setShowFilter(false);
  }, []);

  return (
    <View style={styles.header}>
      <View style={styles.headerBackground}>
        <HeaderBackground />
      </View>

      <View style={styles.headerContent}>
        <TouchableOpacity onPress={onBack} style={styles.leftSection}>
          <AppSvg SvgSrc={iconSvg.arrowLeft} size={16} />
          <Text style={styles.titleText}>{parentCategory?.Name || ''}</Text>
        </TouchableOpacity>

        <RightHeaderActions
          onNotificationPress={handleNotificationPress}
          onCartPress={handleCartPress}
        />
      </View>

      <View style={styles.searchSection}>
        <View style={styles.searchBarContainer}>
          <SearchBar setDataSearch={onSearch} />
        </View>
        <TouchableOpacity onPress={onShowFilter} style={styles.filterButton}>
          <AppSvg
            SvgSrc={iconSvg.filter}
            color="#000"
            size={25}
            style={styles.filterIcon}
          />
        </TouchableOpacity>
      </View>

      <PopupFilterProduct
        visible={showFilter}
        onClose={onCloseFilter}
        onApply={onApplyFilter}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    width: '100%',
    position: 'relative',
    paddingTop: 16,
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  headerContent: {
    marginTop: 40,
    width: '100%',
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 2,
    paddingHorizontal: 12,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
    color: ColorThemes.light.infor_text_color,
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoImage: {
    width: 30,
    height: 30,
    borderRadius: 20,
  },
  iconButton: {
    marginLeft: 8,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  searchSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  searchBarContainer: {
    width: '90%',
    zIndex: 20,
  },
  filterButton: {
    zIndex: 20,
  },
  filterIcon: {
    marginRight: 12,
  },
});

export default CategoryHeader;
