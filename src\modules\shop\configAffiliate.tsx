import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import HeaderShop from '../../components/shop/HeaderShop';

import {ComponentStatus, showSnackbar, Winicon} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {LoadingUI} from '../../features/loading';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import categoryDA from '../category/categoryDA';
import {randomGID} from '../../utils/Utils';
import {ColorThemes} from '../../assets/skin/colors';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import { Snackbar } from 'react-native-paper';

// Tab types for affiliate config
enum AffiliateTabType {
  DEFAULT = 'DEFAULT',
  SHOP = 'SHOP',
  CATEGORY = 'CATEGORY',
}

const ConfigAffiliate = () => {
  const [activeTab, setActiveTab] = useState<AffiliateTabType>(
    AffiliateTabType.DEFAULT,
  );

  const [defaultReward, setDefaultReward] = useState<Array<any>>();
  const [shopReward, setShopReward] = useState<Array<any>>();
  const [hasShopRewardData, setHasShopRewardData] = useState(false);
  const [shopAffiliateEnabled, setShopAffiliateEnabled] = useState(true);

  // Category tab states
  const [categories, setCategories] = useState<Array<any>>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [categoryReward, setCategoryReward] = useState<Array<any>>([]);
  const [childCategoryRewards, setChildCategoryRewards] = useState<Array<any>>(
    [],
  );
  const [hasCategoryRewardData, setHasCategoryRewardData] = useState(false);
  const [categoryLoading, setCategoryLoading] = useState(false);
  const [isEditingParentCategory, setIsEditingParentCategory] = useState(false);
  const [editingChildCategories, setEditingChildCategories] = useState<{
    [key: string]: boolean;
  }>({});

  const [tempValues, setTempValues] = useState<{[key: string]: string}>({});
  const controller = new DataController('Reward');
  const shopController = new DataController('ShopReward');
  const categoryController = new DataController('ShopCate');
  const categoryDataAccess = new categoryDA();
  const [loading, setLoading] = useState(true);
  const shopInfo = useSelectorShopState().data;
  useEffect(() => {
    fetchDefaultReward();
  }, []);

  useEffect(() => {
    if (activeTab === AffiliateTabType.SHOP && defaultReward) {
      fetchShopReward();
    }
  }, [activeTab, defaultReward]);

  useEffect(() => {
    if (activeTab === AffiliateTabType.CATEGORY) {
      const initCategoryTab = async () => {
        await fetchCategories();
        // Đảm bảo có shopReward data để fallback
        if (defaultReward && !shopReward) {
          await fetchShopReward();
        }
      };
      initCategoryTab();
    }
  }, [activeTab]);

  useEffect(() => {
    if (
      activeTab === AffiliateTabType.CATEGORY &&
      selectedCategory &&
      defaultReward
    ) {
      const loadCategoryData = async () => {
        await fetchCategoryReward(selectedCategory);
        await fetchChildCategories(selectedCategory);
      };
      loadCategoryData();
    }
  }, [activeTab, selectedCategory, defaultReward, shopReward, shopAffiliateEnabled]);

  const fetchDefaultReward = async () => {
    const res = await controller.getListSimple({
      query: '*',
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });
    if (res.code === 200) {
      setDefaultReward(res.data);
    }
    setLoading(false);
  };
  //convert name header
  const convertNameHeader = (name: string) => {
    switch (name) {
      case 'Thưởng F2':
        return 'F2';
      case 'Thưởng F1':
        return 'F1';
      case 'Khách hàng':
        return 'KH';
      default:
        return name;
    }
  };
  const fetchShopReward = async () => {
    if (!shopInfo[0]?.Id) return;

    setLoading(true);
    const res = await shopController.getListSimple({
      page: 1,
      size: 100,
      query: `@ShopId: {${shopInfo[0].Id}}`,
      returns: ['Id', 'Name', 'Percent', 'Filial', 'Status'],
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });


    if (res.code === 200 && res.data.length > 0) {
      setShopReward(res.data);
      setHasShopRewardData(true);

      // Kiểm tra trạng thái ON/OFF dựa trên Status của bản ghi đầu tiên
      const firstRecord = res.data[0];
      const isEnabled = firstRecord.Status === 1 || firstRecord.Status === '1';
      debugger
      setShopAffiliateEnabled(isEnabled);
      console.log('Shop has data, status:', firstRecord.Status, 'enabled:', isEnabled);
    } else {
      // Nếu chưa có data trong ShopReward, sử dụng data từ Reward
      setShopReward(
        defaultReward?.map(item => ({...item, isEdit: false, })) || [],
      );
      setHasShopRewardData(false);
      setShopAffiliateEnabled(true); // Mặc định là ON khi chưa có dữ liệu
      console.log('Shop has no data, default to ON');
    }
    setLoading(false);
  };

  const insertAllToShopReward = async (updatedData: any[]) => {
    if (!shopInfo[0]?.Id) return false;

    const dataToInsert = updatedData.map(item => ({
      Id: randomGID(),
      DateCreated: new Date().getTime(),
      Name: item.Name,
      Percent: item.Percent,
      ShopId: shopInfo[0].Id,
      Filial: item.Filial,
      Sort: item.Sort,
      Status: 1,
    }));
    
    const res = await shopController.add(dataToInsert);
    return res.code === 200;
  };

  const updateShopRewardItem = async (itemId: string, percent: number) => {
    const res = await shopController.edit([
      {
        Id: itemId,
        Percent: percent,
      },
    ]);
    return res.code === 200;
  };

  // Hàm xử lý toggle ON/OFF cho Shop
  const handleShopAffiliateToggle = async (enabled: boolean) => {
    try {
      if (!shopInfo[0]?.Id) {
        showSnackbar({
          message: 'Không thể cập nhật trạng thái, vui lòng thử lại sau',
          status: ComponentStatus.ERROR,
        });
        return;
      }

      // Cập nhật UI ngay lập tức để có phản hồi tức thì
      setShopAffiliateEnabled(enabled);

      if (!hasShopRewardData) {
        // Chưa có dữ liệu Shop -> Insert toàn bộ với Status tương ứng
        console.log('Inserting new shop reward data with status:', enabled ? 1 : 0);

        const dataToInsert = defaultReward?.map((item, index) => ({
          Id: randomGID(),
          DateCreated: new Date().getTime(),
          Name: item.Name,
          Percent: item.Percent,
          ShopId: shopInfo[0].Id,
          Filial: parseInt(item.Filial),
          Sort: index + 1,
          Status: enabled ? 1 : 0,
        })) || [];

        const res = await shopController.add(dataToInsert);
        if (res.code === 200) {
          setHasShopRewardData(true);
          await fetchShopReward(); // Refresh data
        } else {
          // Nếu API thất bại, revert lại trạng thái UI
          setShopAffiliateEnabled(!enabled);
          showSnackbar({
            message: 'Không thể cập nhật trạng thái, vui lòng thử lại sau',
            status: ComponentStatus.ERROR,
          });
        }
      } else {
        // Đã có dữ liệu Shop -> Update Status của tất cả bản ghi
        console.log('Updating existing shop reward status to:', enabled ? 1 : 0);

        const updateData = shopReward?.map(item => ({
          Id: item.Id,
          Status: enabled ? 1 : 0,
        })) || [];

        const results = await Promise.all(
          updateData.map(item => shopController.edit([item]))
        );

        const allSuccess = results.every(r => r.code === 200);
        if (allSuccess) {
          await fetchShopReward(); // Refresh data
          console.log('Shop status updated successfully, new state:', enabled);
        } else {
          // Nếu API thất bại, revert lại trạng thái UI
          console.log('Failed to update shop status, reverting UI');
          setShopAffiliateEnabled(!enabled);
          showSnackbar({
            message: 'Không thể cập nhật trạng thái, vui lòng thử lại sau',
            status: ComponentStatus.ERROR,
          });
        }
      }
    } catch (error) {
      console.error('Error toggling shop affiliate:', error);
      // Nếu có lỗi, revert lại trạng thái UI
      setShopAffiliateEnabled(!enabled);
      showSnackbar({
        message: 'Không thể cập nhật trạng thái, vui lòng thử lại sau',
        status: ComponentStatus.ERROR,
      });
    }
  };

  // Category functions
  const fetchCategories = async () => {
    try {
      console.log('Fetching categories...');
      const res = await categoryDataAccess.getCategories();
      console.log('Categories response:', res);

      if (res?.code === 200 && res.data && res.data.length > 0) {
        setCategories(res.data);
        console.log('Categories set:', res.data);

        if (!selectedCategory) {
          const parentCategory = res.data.find((category: any) => !category.ParentId);
          const selectedId = parentCategory?.Id || res.data[0].Id;
          setSelectedCategory(selectedId);
          console.log('Selected category set to:', selectedId);
        }
      } else {
        console.log('No categories found or invalid response');
        setCategories([]);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategories([]);
    }
  };

  const fetchChildCategories = async (parentId: string) => {
    try {
      console.log('Fetching child categories for parent:', parentId);
      console.log('All categories:', categories);

      const res = categories.filter(category => category.ParentId === parentId);
      console.log('Child categories found:', res);

      await fetchChildCategoryRewards(res);
    } catch (error) {
      console.error('Error fetching child categories:', error);
    }
  };

  const fetchCategoryReward = async (categoryId: string) => {
    if (!categoryId || !shopInfo[0]?.Id) return;

    setCategoryLoading(true);
    // Fetch from ShopCate table first
    const res = await categoryController.getListSimple({
      page: 1,
      size: 100,
      query: `@CategoryId: {${categoryId}} @ShopId: {${shopInfo[0].Id}}`,
      returns: ['Id', 'Name', 'Percent', 'CategoryId', 'Filial'],
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });

    if (res.code === 200 && res.data.length > 0) {
      setCategoryReward(
        res.data.map((item: any) => ({...item, isEdit: false})),
      );
      setHasCategoryRewardData(true);
    } else {
      // Nếu chưa có data trong ShopCate, lấy từ ShopReward trước (chỉ khi Status = 1)
      console.log('Shop affiliate enabled:', shopAffiliateEnabled);
      console.log('Has shop reward data:', hasShopRewardData);

      if (shopReward && shopReward.length > 0 && hasShopRewardData && shopAffiliateEnabled) {
        console.log('Using shop reward data for category');
        setCategoryReward(
          shopReward?.map(item => ({...item, isEdit: false})) || [],
        );
      } else {
        // Nếu chưa có ShopReward hoặc Shop bị tắt, sử dụng data từ Default
        console.log('Using default reward data for category');
        setCategoryReward(
          defaultReward?.map(item => ({...item, isEdit: false})) || [],
        );
      }
      setHasCategoryRewardData(false);
    }
    setCategoryLoading(false);
  };

  const fetchChildCategoryRewards = async (childCats: any[]) => {
    if (!childCats.length || !shopInfo[0]?.Id) return;

    const childRewards = [];
    for (const child of childCats) {
      const res = await categoryController.getListSimple({
        page: 1,
        size: 100,
        query: `@CategoryId: {${child.Id}} @ShopId: {${shopInfo[0].Id}}`,
        returns: ['Id', 'Name', 'Percent', 'CategoryId', 'Filial'],
        sortby: {BY: 'Sort', DIRECTION: 'ASC'},
      });

      if (res.code === 200 && res.data.length > 0) {
        childRewards.push({
          categoryId: child.Id,
          categoryName: child.Name,
          rewards: res.data.map((item: any) => ({...item, isEdit: false})),
          hasData: true,
        });
      } else {
        // Nếu chưa có data trong ShopCate cho danh mục con, lấy từ ShopReward trước (chỉ khi Status = 1)
        let defaultData;
        if (shopReward && shopReward.length > 0 && hasShopRewardData && shopAffiliateEnabled) {
          defaultData = shopReward?.map(item => ({...item, isEdit: false})) || [];
        } else {
          // Nếu chưa có ShopReward hoặc Shop bị tắt, sử dụng data từ Default
          defaultData = defaultReward?.map(item => ({...item, isEdit: false})) || [];
        }

        childRewards.push({
          categoryId: child.Id,
          categoryName: child.Name,
          rewards: defaultData,
          hasData: false,
        });
      }
    }
    setChildCategoryRewards(childRewards);
  };

  const insertAllToCategoryReward = async (
    categoryId: string,
    updatedData: any[],
  ) => {
    try {
      console.log('insertAllToCategoryReward called with:', { categoryId, updatedData });

      if (!shopInfo[0]?.Id) {
        console.log('No shop info available');
        return false;
      }

      const dataToInsert = updatedData.map((item: any, index: number) => ({
        Id: randomGID(),
        DateCreated: new Date().getTime(),
        Name: item.Name,
        Percent: item.Percent,
        CategoryId: categoryId,
        ShopId: shopInfo[0].Id,
        Filial: parseInt(item.Filial),
        Sort: index + 1,
        Status: 1,
      }));

      console.log('Data to insert:', dataToInsert);

      const res = await categoryController.add(dataToInsert);
      debugger
      console.log('Insert result:', res);

      if (res && res.code === 200) {
        //gán lại id mới cho cate child
        const newChildRewards = childCategoryRewards.map(cr => {
          if (cr.categoryId === categoryId) {
            return {
              ...cr,
              rewards: cr.rewards.map((item: any) => {
                const newItem = dataToInsert.find(
                  (newItem: any) => newItem.Name === item.Name,
                );
                return newItem ? {...newItem, isEdit: false} : item;
              }),
            };
          }
          return cr;
        });
        setChildCategoryRewards(newChildRewards);
        console.log('Successfully inserted category reward data');
        return true;
      }
      console.log('Failed to insert category reward data');
      return false;
    } catch (error) {
      console.error('Error in insertAllToCategoryReward:', error);
      return false;
    }
  };

  const updateCategoryRewardItem = async (itemId: string, percent: number) => {
    const res = await categoryController.edit([
      {
        Id: itemId,
        Percent: percent,
      },
    ]);
    return res.code === 200;
  };

  const updateAllCategoryRewards = async (updatedData: any[]) => {
    if (!shopInfo[0]?.Id) return false;

    const res = await Promise.all(
      updatedData.map((item: any) =>
        categoryController.edit([
          {
            Id: item.Id,
            Percent: item.Percent,
          },
        ]),
      ),
    );
    return res.every(r => r.code === 200);
  };

  // Hàm insert/update toàn bộ danh mục con khi cấu hình toàn danh mục
  const insertOrUpdateAllChildCategories = async (parentCategoryId: string, updatedRewards: any[]) => {
    try {
      if (!shopInfo[0]?.Id) {
        console.log('No shop info available');
        return false;
      }

      if (!categories || categories.length === 0) {
        console.log('No categories data available');
        return true;
      }

      const childCategories = categories.filter(cat => cat.ParentId === parentCategoryId);
      console.log('Child categories found:', childCategories.length);
      console.log('Child categories:', childCategories);

      if (childCategories.length === 0) {
        console.log('No child categories to update');
        return true;
      }

      for (const childCat of childCategories) {
        console.log(`Processing child category: ${childCat.Name} (ID: ${childCat.Id})`);
        const existingChildReward = childCategoryRewards.find(cr => cr.categoryId === childCat.Id);
        console.log('Found existing child reward:', existingChildReward);

        if (existingChildReward && existingChildReward.hasData) {
          // Danh mục con đã có dữ liệu -> UPDATE theo % của danh mục cha
          console.log(`Updating existing data for child category: ${childCat.Name}`);
          console.log('Existing child reward:', existingChildReward);
          console.log('Updated rewards to apply:', updatedRewards);

          const updateData = updatedRewards.map((item: any) => {
            // Tìm bản ghi tương ứng trong danh mục con để lấy Id
            const existingReward = existingChildReward.rewards.find((reward: any) =>
              parseInt(reward.Filial) === parseInt(item.Filial)
            );
            console.log(`Looking for Filial ${item.Filial}, found:`, existingReward);
            return {
              Id: existingReward?.Id,
              Percent: item.Percent,
            };
          }).filter(item => item.Id); // Chỉ lấy những item có Id

          console.log('Update data prepared:', updateData);

          if (updateData.length > 0) {
            const result = await Promise.all(
              updateData.map((item: any) =>
                categoryController.edit([item])
              )
            );

            console.log('Update results:', result);
            const allSuccess = result.every(r => r.code === 200);
            if (allSuccess) {
              console.log(`Successfully updated data for child category: ${childCat.Name}`);
            } else {
              console.error(`Failed to update some data for child category ${childCat.Name}`);
            }
          } else {
            console.log(`No data to update for child category: ${childCat.Name}`);
          }
        } else {
          // Danh mục con chưa có dữ liệu -> INSERT mới
          console.log(`Adding new data for child category: ${childCat.Name}`);
          const dataToInsert = updatedRewards.map((item: any, index: number) => ({
            Id: randomGID(),
            DateCreated: new Date().getTime(),
            Name: item.Name,
            Percent: item.Percent,
            CategoryId: childCat.Id,
            ShopId: shopInfo[0].Id,
            Filial: parseInt(item.Filial),
            Sort: index + 1,
            Status: 1,
          }));

          const result = await categoryController.add(dataToInsert);
          if (result.code !== 200) {
            console.error(`Failed to add data for child category ${childCat.Name}:`, result);
          } else {
            console.log(`Successfully added data for child category: ${childCat.Name}`);
          }
        }
      }
      return true;
    } catch (error) {
      console.error('Error in insertOrUpdateAllChildCategories:', error);
      return false;
    }
  };



  const handleTempValueChange = (itemId: string, value: string) => {
    // Only allow numbers and decimal point
    const numericValue = value.replace(/[^0-9.]/g, '');

    // If empty, set to "0"
    if (numericValue === '') {
      setTempValues(prev => ({
        ...prev,
        [itemId]: '0',
      }));
      return;
    }

    // Get current value from state
    const currentValue = tempValues[itemId] || '0';
    let cleanValue;

    // Special handling: if current is "0" and new input starts with "0" but is not "0" or "0."
    // This means user typed a number after "0", so we need to replace "0"
    if (currentValue === '0' && numericValue.startsWith('0') && numericValue.length > 1 && !numericValue.startsWith('0.')) {
      // Remove the leading "0" - user typed "05" but we want "5"
      cleanValue = numericValue.substring(1);
    } else if (currentValue === '0' && numericValue !== '0' && !numericValue.startsWith('0.')) {
      // Direct replacement when typing non-zero after "0"
      cleanValue = numericValue;
    } else {
      // Prevent multiple decimal points
      const parts = numericValue.split('.');
      cleanValue = parts.length > 2
        ? parts[0] + '.' + parts.slice(1).join('')
        : numericValue;
    }

    // Limit to 100
    const numValue = parseFloat(cleanValue);
    if (!isNaN(numValue) && numValue > 100) {
      return; // Don't update if value exceeds 100
    }

    setTempValues(prev => ({
      ...prev,
      [itemId]: cleanValue,
    }));
  };

  const handleEditPress = (item: any) => {
    // Keep the current value when editing
    setTempValues(prev => ({
      ...prev,
      [item.Id]: item.Percent.toString(),
    }));

    if (activeTab === AffiliateTabType.SHOP) {
      // Update shopReward to set isEdit = true for this item
      setShopReward(
        prev =>
          prev?.map(reward =>
            reward.Id === item.Id ? {...reward, isEdit: true} : reward,
          ) || [],
      );
    } else if (activeTab === AffiliateTabType.CATEGORY) {
      // Update categoryReward to set isEdit = true for this item
      setCategoryReward(
        prev =>
          prev?.map(reward =>
            reward.Id === item.Id ? {...reward, isEdit: true} : reward,
          ) || [],
      );
    }
  };

  // Handle edit for parent category (all rewards at once)
  const handleEditParentCategory = () => {
    setIsEditingParentCategory(true);
    // Keep current values for all rewards
    categoryReward?.forEach((item: any) => {
      setTempValues(prev => ({
        ...prev,
        [item.Id || item.RewardId]: item.Percent.toString(),
      }));
    });
  };

  // Handle edit for child category
  const handleEditChildCategory = (categoryId: string, rewards: any[]) => {
    setEditingChildCategories(prev => ({
      ...prev,
      [categoryId]: true,
    }));
    // Keep current values for all rewards in this child category
    rewards.forEach((item: any) => {
      setTempValues(prev => ({
        ...prev,
        [`${categoryId}_${item.Id || item.RewardId}`]: item.Percent.toString(),
      }));
    });
  };

  const handleSavePress = async (item: any) => {
    const inputValue = tempValues[item.Id] || item.Percent.toString();
    const newPercent = parseFloat(inputValue);

    if (isNaN(newPercent) || newPercent < 0) {
      showSnackbar({
        message: 'Vui lòng nhập số hợp lệ (0-100)',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    if (newPercent > 100) {
      showSnackbar({
        message: 'Vui lòng nhập số không vượt quá 100',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    try {
      if (activeTab === AffiliateTabType.SHOP) {
        if (hasShopRewardData) {
          // Nếu đã có data trong ShopReward, chỉ update item này
          const success = await updateShopRewardItem(item.Id, newPercent);
          if (success) {
            setShopReward(
              prev =>
                prev?.map(reward =>
                  reward.Id === item.Id
                    ? {...reward, Percent: newPercent, isEdit: false}
                    : reward,
                ) || [],
            );
          }
        } else {
          // Nếu chưa có data trong ShopReward, insert toàn bộ với giá trị đã thay đổi
          const updatedData =
            shopReward?.map(reward =>
              reward.Id === item.Id ? {...reward, Percent: newPercent} : reward,
            ) || [];

          const success = await insertAllToShopReward(updatedData);
          if (success) {
            setHasShopRewardData(true);
            // Fetch lại data từ ShopReward
            await fetchShopReward();
          }
        }
      } else if (activeTab === AffiliateTabType.CATEGORY) {
        if (hasCategoryRewardData) {
          // Nếu đã có data trong CategoryReward, chỉ update item này
          const success = await updateCategoryRewardItem(item.Id, newPercent);
          if (success) {
            setCategoryReward(
              prev =>
                prev?.map(reward =>
                  reward.Id === item.Id
                    ? {...reward, Percent: newPercent, isEdit: false}
                    : reward,
                ) || [],
            );
          }
        } else {
          // Nếu chưa có data trong CategoryReward, insert toàn bộ với giá trị đã thay đổi
          const updatedData =
            categoryReward?.map(reward =>
              reward.Id === item.Id ? {...reward, Percent: newPercent} : reward,
            ) || [];

          const success = await insertAllToCategoryReward(
            selectedCategory,
            updatedData,
          );
          if (success) {
            setHasCategoryRewardData(true);
            // Fetch lại data từ CategoryReward
            await fetchCategoryReward(selectedCategory);
          }
        }
      }
    } catch (error) {
      console.error('Error saving reward:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi lưu cấu hình',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const handleCancelPress = (item: any) => {
    if (activeTab === AffiliateTabType.SHOP) {
      setShopReward(
        prev =>
          prev?.map(reward =>
            reward.Id === item.Id ? {...reward, isEdit: false} : reward,
          ) || [],
      );
    } else if (activeTab === AffiliateTabType.CATEGORY) {
      setCategoryReward(
        prev =>
          prev?.map(reward =>
            reward.Id === item.Id ? {...reward, isEdit: false} : reward,
          ) || [],
      );
    }

    setTempValues(prev => {
      const newValues = {...prev};
      delete newValues[item.Id];
      return newValues;
    });
  };

  // Handle save for parent category (all rewards at once)
  const handleSaveParentCategory = async () => {
    try {
      console.log('Starting handleSaveParentCategory...');
      console.log('Selected category:', selectedCategory);
      console.log('Has category reward data:', hasCategoryRewardData);
      console.log('Category reward:', categoryReward);
      console.log('Child category rewards:', childCategoryRewards);

      // Validate all percentages before saving
      const hasInvalidValue = categoryReward?.some((item: any) => {
        const inputValue = tempValues[item.Id || item.RewardId] || item.Percent.toString();
        const percent = parseFloat(inputValue);
        return isNaN(percent) || percent < 0 || percent > 100;
      });

      if (hasInvalidValue) {
        showSnackbar({
          message: 'Vui lòng nhập số hợp lệ (0-100) cho tất cả các trường',
          status: ComponentStatus.ERROR,
        });
        return;
      }
      debugger
      const updatedRewards =
        categoryReward?.map((item: any) => ({
          ...item,
          Percent: parseFloat(
            tempValues[item.Id || item.RewardId] || item.Percent.toString(),
          ),
        })) || [];

      console.log('Updated rewards:', updatedRewards);

      if (hasCategoryRewardData) {
        // Update existing records for parent category
        console.log('Updating existing parent category data...');
        const success = await updateAllCategoryRewards(updatedRewards);
        console.log('Parent category update success:', success);

        if (success) {
          // Update/Insert cho tất cả danh mục con (chỉ những danh mục con chưa có dữ liệu riêng)
          console.log('Updating child categories...');
          const childSuccess = await insertOrUpdateAllChildCategories(selectedCategory, updatedRewards);
          console.log('Child categories update success:', childSuccess);

          setIsEditingParentCategory(false);
          // Clear temp values
          categoryReward?.forEach((item: any) => {
            setTempValues(prev => {
              const newValues = {...prev};
              delete newValues[item.Id || item.RewardId];
              return newValues;
            });
          });
          // Fetch lại data để có Id mới nhất
          await fetchCategoryReward(selectedCategory);
          await fetchChildCategories(selectedCategory);
        } else {
          showSnackbar({
            message: 'Có lỗi xảy ra khi lưu cấu hình',
            status: ComponentStatus.ERROR,
          });
        }
      } else {
        // Insert new records for parent category
        console.log('Inserting new parent category data...');
        const success = await insertAllToCategoryReward(
          selectedCategory,
          updatedRewards,
        );
        console.log('Parent category insert success:', success);

        if (success) {
          setHasCategoryRewardData(true);

          // Insert cho tất cả danh mục con
          console.log('Inserting child categories...');
          const childSuccess = await insertOrUpdateAllChildCategories(selectedCategory, updatedRewards);
          console.log('Child categories insert success:', childSuccess);

          setIsEditingParentCategory(false);
          // Clear temp values
          categoryReward?.forEach((item: any) => {
            setTempValues(prev => {
              const newValues = {...prev};
              delete newValues[item.Id || item.RewardId];
              return newValues;
            });
          });
          // Fetch lại data để có Id mới từ ShopCate
          await fetchCategoryReward(selectedCategory);
          await fetchChildCategories(selectedCategory);
        } else {
          showSnackbar({
            message: 'Có lỗi xảy ra khi lưu cấu hình',
            status: ComponentStatus.ERROR,
          });
        }
      }
    } catch (error) {
      console.error('Error saving parent category rewards:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi lưu cấu hình',
        status: ComponentStatus.ERROR,
      });
    }
  };

  // Handle cancel for parent category
  const handleCancelParentCategory = () => {
    setIsEditingParentCategory(false);
    // Clear temp values for parent category
    categoryReward?.forEach((item: any) => {
      setTempValues(prev => {
        const newValues = {...prev};
        delete newValues[item.Id || item.RewardId];
        return newValues;
      });
    });
  };

  // Handle save for child category
  const handleSaveChildCategory = async (categoryId: string) => {
    try {
      const childReward = childCategoryRewards.find(
        cr => cr.categoryId === categoryId,
      );
      if (!childReward) return;

      // Validate all percentages before saving
      const hasInvalidValue = childReward.rewards.some((item: any) => {
        const inputValue = tempValues[`${categoryId}_${item.Id || item.RewardId}`] || item.Percent.toString();
        const percent = parseFloat(inputValue);
        return isNaN(percent) || percent < 0 || percent > 100;
      });

      if (hasInvalidValue) {
        showSnackbar({
          message: 'Vui lòng nhập số hợp lệ (0-100) cho tất cả các trường',
          status: ComponentStatus.ERROR,
        });
        return;
      }

      const updatedRewards = childReward.rewards.map((item: any) => ({
        ...item,
        Percent: parseFloat(
          tempValues[`${categoryId}_${item.Id || item.RewardId}`] || item.Percent.toString(),
        ),
      }));
      debugger
      if (childReward.hasData) {
        // Update existing records
        const success = await updateAllCategoryRewards(updatedRewards);
        if (success) {
          setEditingChildCategories(prev => ({...prev, [categoryId]: false}));
          // Clear temp values for this child category
          const childReward = childCategoryRewards.find(
            cr => cr.categoryId === categoryId,
          );
          childReward?.rewards.forEach((item: any) => {
            setTempValues(prev => {
              const newValues = {...prev};
              delete newValues[`${categoryId}_${item.Id || item.RewardId}`];
              return newValues;
            });
          });
          // Fetch lại data để có Id mới nhất
          await fetchChildCategories(selectedCategory);
        }
      } else {
        // Insert new records
        const success = await insertAllToCategoryReward(
          categoryId,
          updatedRewards,
        );
        if (success) {
          setEditingChildCategories(prev => ({...prev, [categoryId]: false}));
          // Clear temp values for this child category
          const childReward = childCategoryRewards.find(
            cr => cr.categoryId === categoryId,
          );
          childReward?.rewards.forEach((item: any) => {
            setTempValues(prev => {
              const newValues = {...prev};
              delete newValues[`${categoryId}_${item.Id || item.RewardId}`];
              return newValues;
            });
          });
          // Fetch lại data để có Id mới từ ShopCate
          await fetchChildCategories(selectedCategory);
        }
      }
    } catch (error) {
      console.error('Error saving child category rewards:', error);
    }
  };

  // Handle cancel for child category
  const handleCancelChildCategory = (categoryId: string) => {
    setEditingChildCategories(prev => ({...prev, [categoryId]: false}));
    // Clear temp values for this child category
    const childReward = childCategoryRewards.find(
      cr => cr.categoryId === categoryId,
    );
    childReward?.rewards.forEach((item: any) => {
      setTempValues(prev => {
        const newValues = {...prev};
        delete newValues[`${categoryId}_${item.Id || item.RewardId}`];
        return newValues;
      });
    });
  };

  // Tab configuration
  const tabs = [
    {
      id: AffiliateTabType.DEFAULT,
      name: 'Mặc định',
    },
    {
      id: AffiliateTabType.SHOP,
      name: 'SHOP',
    },
    {
      id: AffiliateTabType.CATEGORY,
      name: 'Danh mục',
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case AffiliateTabType.DEFAULT:
        return renderDefaultSettings();
      case AffiliateTabType.SHOP:
        return renderShopSettings();
      case AffiliateTabType.CATEGORY:
        return renderCategorySettings();
      default:
        return renderDefaultSettings();
    }
  };

  const renderDefaultSettings = () => {
    return (
      <ScrollView style={styles.tabContent}>
        {loading ? (
          <LoadingUI isLoading={loading} />
        ) : defaultReward && defaultReward.length > 0 ? (
          defaultReward.map((item: any) => (
            <View key={item.Id} style={styles.commissionItem}>
              <Text style={styles.commissionLabel}>{item.Name}</Text>
              <View style={styles.percentageContainer}>
                <Text style={styles.percentageInput}>{item.Percent}%</Text>
              </View>
            </View>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Winicon src="outline/business/money" size={48} color="#999" />
            <Text style={styles.emptyText}>Chưa có cấu hình mặc định</Text>
          </View>
        )}
      </ScrollView>
    );
  };

  const renderCommissionRow = (item: any) => (
    <View key={item.Id} style={styles.commissionItem}>
      <Text style={styles.commissionLabel}>{item.Name}</Text>
      <View
        style={{
          ...styles.percentageContainer,
          backgroundColor: '#fff',
          width: 150,
        }}>
        {item.isEdit ? (
          <TextInput
            value={tempValues[item.Id] || item.Percent.toString()}
            onChangeText={(value: string) =>
              handleTempValueChange(item.Id, value)
            }
            style={styles.percentageInputEdit}
            keyboardType="numeric"
            autoFocus
            selectTextOnFocus={true}
            placeholder="0-100"
            placeholderTextColor="#999"
          />
        ) : (
          <Text style={styles.percentageInput}>{`${item.Percent !== '-' ? `${item.Percent}%` : '--'}`}</Text>
        )}
      </View>
      <View style={styles.editActionsContainer}>
        {item.isEdit ? (
          <>
            <TouchableOpacity
              style={styles.saveButton}
              onPress={() => handleSavePress(item)}>
              <Text style={styles.saveButtonText}>✓</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => handleCancelPress(item)}>
              <Text style={styles.cancelButtonText}>✕</Text>
            </TouchableOpacity>
          </>
        ) : (
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => handleEditPress(item)}>
            <Winicon src="fill/user interface/i-edit" size={15} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderShopSettings = () => {
    return (
      <ScrollView style={styles.tabContent}>
        {loading ? (
          <LoadingUI isLoading={loading} />
        ) : shopReward && shopReward.length > 0 ? (
          shopReward.map((item: any) => renderCommissionRow(item))
        ) : (
          <View style={styles.emptyState}>
            <Winicon src="outline/business/shop" size={48} color="#999" />
            <Text style={styles.emptyText}>Chưa có cấu hình shop</Text>
          </View>
        )}

        {/* Toggle Switch - hiển thị luôn */}
        <View style={styles.toggleContainer}>
          <Text style={styles.toggleLabel}>{shopAffiliateEnabled
            ? 'ON'
            : 'OFF'
         }</Text>
          <TouchableOpacity
            style={[
              styles.toggleSwitch,
              shopAffiliateEnabled && styles.toggleSwitchActive,
            ]}
            onPress={() => {
              console.log('Toggle pressed, current state:', shopAffiliateEnabled, 'will change to:', !shopAffiliateEnabled);
              handleShopAffiliateToggle(!shopAffiliateEnabled);
            }}>
            <View
              style={[
                styles.toggleThumb,
                shopAffiliateEnabled && styles.toggleThumbActive,
              ]}
            />
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  };

  const renderCategoryTags = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.categoryTagsContainer}
      contentContainerStyle={styles.categoryTagsContent}>
      {categories
        .filter(category => !category.ParentId)
        .map(category => (
          <TouchableOpacity
            key={category.Id}
            style={[
              styles.categoryTag,
              selectedCategory === category.Id && styles.categoryTagSelected,
            ]}
            onPress={() => setSelectedCategory(category.Id)}>
            <Text
              style={[
                styles.categoryTagText,
                selectedCategory === category.Id &&
                  styles.categoryTagTextSelected,
              ]}>
              {category.Name}
            </Text>
          </TouchableOpacity>
        ))}
    </ScrollView>
  );

  const renderCategoryTable = () => (
    <View style={styles.categoryTableContainer}>
      {/* Table Header */}
      <View style={styles.tableHeader}>
        <View style={styles.tableHeaderFirstColumn}>
          <Text style={styles.tableHeaderText}>Toàn danh mục</Text>
        </View>
        {defaultReward?.map(reward => (
          <View key={reward.Id} style={styles.tableHeaderColumn}>
            <Text style={styles.tableHeaderText}>
              {convertNameHeader(reward.Name) || reward.Name}
            </Text>
            <Winicon src="fill/arrows/chevron-down" size={12} color="#666" />
          </View>
        ))}
        <View style={styles.tableHeaderColumn}>
          <Text style={styles.tableHeaderText}>TT</Text>
        </View>
      </View>

      {/* Table Row */}
      <View style={styles.tableRow}>
        <View style={styles.tableFirstColumn}>
          <Text style={styles.tableRowLabel}>Toàn danh mục</Text>
        </View>
        {isEditingParentCategory ? (
          // Edit mode - show all inputs
          <>
            {categoryReward?.map(item => (
              <View key={item.Id || item.RewardId} style={styles.tableCell}>
                <TextInput
                  value={
                    tempValues[item.Id || item.RewardId] || item.Percent.toString()
                  }
                  onChangeText={(value: string) =>
                    handleTempValueChange(item.Id || item.RewardId, value)
                  }
                  style={styles.tableCellInput}
                  keyboardType="numeric"
                  selectTextOnFocus={true}
                  placeholder="0-100"
                  placeholderTextColor="#999"
                />
              </View>
            ))}
            <View style={styles.tableActions}>
              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleSaveParentCategory}>
                <Text style={styles.saveButtonText}>✓</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={handleCancelParentCategory}>
                <Text style={styles.cancelButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          // View mode - show percentages
          <>
            {categoryReward?.map(item => (
              <View key={item.Id || item.RewardId} style={styles.tableCell}>
                <Text style={styles.tableCellText}>{`${item.Percent !== '-' ? `${item.Percent}%` : '--'}`}</Text>
              </View>
            ))}
            <View style={styles.tableActions}>
              <TouchableOpacity
                style={styles.editButton}
                onPress={handleEditParentCategory}>
                <Winicon src="fill/user interface/i-edit" size={15} />
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    </View>
  );

  const renderChildCategoriesTable = () => (
    <View style={styles.childCategoriesContainer}>
      <View
        key={'childReward.categoryId'}
        style={styles.categoryTableContainer}>
        {/* Child Category Header */}
        <View style={styles.tableHeader}>
          <View style={styles.tableHeaderFirstColumn}>
            <Text style={styles.tableHeaderText}>Danh mục</Text>
          </View>
          {defaultReward?.map(reward => (
            <View key={reward.Id} style={styles.tableHeaderColumn}>
              <Text style={styles.tableHeaderText}>
                {convertNameHeader(reward.Name) || reward.Name}
              </Text>
              <Winicon src="fill/arrows/chevron-down" size={12} color="#666" />
            </View>
          ))}
          <View style={styles.tableHeaderColumn}>
            <Text style={styles.tableHeaderText}>TT</Text>
          </View>
        </View>
        {childCategoryRewards.map(childReward => (
          <View key={childReward.categoryId} style={styles.tableRow}>
            <View style={styles.tableFirstColumn}>
              <Text style={styles.tableRowLabel}>
                {childReward.categoryName}
              </Text>
            </View>
            {editingChildCategories[childReward.categoryId] ? (
              // Edit mode
              <>
                {childReward.rewards.map((item: any) => (
                  <View key={item.Id || item.RewardId} style={styles.tableCell}>
                    <TextInput
                      value={
                        tempValues[
                          `${childReward.categoryId}_${
                            item.Id || item.RewardId
                          }`
                        ] || item.Percent.toString()
                      }
                      onChangeText={(value: string) =>
                        handleTempValueChange(
                          `${childReward.categoryId}_${
                            item.Id || item.RewardId
                          }`,
                          value,
                        )
                      }
                      style={styles.tableCellInput}
                      keyboardType="numeric"
                      selectTextOnFocus={true}
                      placeholder="0-100"
                      placeholderTextColor="#999"
                    />
                  </View>
                ))}
                <View style={styles.tableActions}>
                  <TouchableOpacity
                    style={styles.saveButton}
                    onPress={() =>
                      handleSaveChildCategory(childReward.categoryId)
                    }>
                    <Text style={styles.saveButtonText}>✓</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() =>
                      handleCancelChildCategory(childReward.categoryId)
                    }>
                    <Text style={styles.cancelButtonText}>✕</Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : (
              // View mode
              <>
                {childReward.rewards.map((item: any) => (
                  <View key={item.Id || item.RewardId} style={styles.tableCell}>
                    <Text style={styles.tableCellText}>{`${item.Percent !== '-' ? `${item.Percent}%` : '--'}`}</Text>
                  </View>
                ))}
                <View style={styles.tableActions}>
                  <TouchableOpacity
                    style={styles.editButton}
                    onPress={() =>
                      handleEditChildCategory(
                        childReward.categoryId,
                        childReward.rewards,
                      )
                    }>
                    <Winicon src="fill/user interface/i-edit" size={15} />
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        ))}
      </View>
    </View>
  );

  const renderCategorySettings = () => (
    <ScrollView style={styles.tabContent}>
      <Text
        style={{
          ...styles.categoryDescription,
          color: '#000',
          fontWeight: 'bold',
          fontSize: 16,
          marginBottom: 0,
        }}>
        Bảng cấu hình affiliate theo danh mục
      </Text>
      <Text style={{...styles.categoryDescription}}>
        Ưu tiên trả thưởng: Danh mục con → Danh mục lớn → SHOP → Mặc định
      </Text>

      {/* Category Tags */}
      {renderCategoryTags()}

      {/* Parent Category Table */}
      {categoryLoading ? (
        <LoadingUI isLoading={categoryLoading} />
      ) : categoryReward && categoryReward.length > 0 ? (
        renderCategoryTable()
      ) : (
        <View style={styles.emptyState}>
          <Winicon src="outline/business/tag" size={48} color="#999" />
          <Text style={styles.emptyText}>Chưa có cấu hình danh mục</Text>
        </View>
      )}

      {/* Child Categories Tables */}
      {!categoryLoading && childCategoryRewards.length > 0
        ? renderChildCategoriesTable()
        : !categoryLoading &&
          categoryReward &&
          categoryReward.length > 0 && (
            <View style={styles.emptyState}>
              <Winicon src="outline/business/tag" size={48} color="#999" />
              <Text style={styles.emptyText}>Không có danh mục con</Text>
            </View>
          )}
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <InforHeader title={'Cấu hình affiliate'} />
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab.id}
            style={styles.tab}
            onPress={() => setActiveTab(tab.id)}>
            <Text
              style={[
                styles.tabText,
                activeTab === tab.id && styles.activeTabText,
              ]}>
              {tab.name}
            </Text>
            {activeTab === tab.id && <View style={styles.activeTabIndicator} />}
          </TouchableOpacity>
        ))}
      </View>

      {/* Tab Content */}
      <View style={styles.content}>{renderTabContent()}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  navigator: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#00FFFF',
    paddingBottom: 18,
    borderBottomWidth: 0.5,
  },
  content: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  // Tab Navigation Styles
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 0,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    paddingBottom: 16,
    position: 'relative',
  },
  tabText: {
    fontSize: 14,
    color: '#999',
    fontWeight: '400',
  },
  activeTabText: {
    color: '#00BFFF',
    fontWeight: '600',
  },
  activeTabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: '#00BFFF',
  },
  // Content Styles
  tabContent: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  // Commission Item Styles
  commissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  commissionLabel: {
    fontSize: 14,
    color: '#333',
    fontWeight: '400',
    flex: 1,
  },
  percentageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 6,
    paddingVertical: 10,
  },
  percentageInput: {
    fontSize: 11,
    color: '#333',
    fontWeight: '600',
    textAlign: 'left',
    minWidth: 200,
    borderWidth: 0,
    padding: 16,
  },
  // Empty State
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
  // Toggle Switch Styles
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    backgroundColor: '#fff',
    marginVertical: 10,
  },
  toggleLabel: {
    fontSize: 14,
    marginRight: 10,
    color: '#333',
    fontWeight: '400',
  },
  toggleSwitch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#ccc',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 5,
  },
  toggleSwitchActive: {
    backgroundColor: '#1C33FF',
  },
  toggleThumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#fff',
  },
  toggleThumbActive: {
    transform: [{translateX: 12}],
  },
  // Edit Button Styles
  editButtonContainer: {
    alignItems: 'flex-end',
    marginBottom: 16,
  },
  editButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    minWidth: 50,
    alignItems: 'center',
  },
  editButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  saveButton: {
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 24,
    minWidth: 30,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#4CAF50',
    fontSize: 12,
    fontWeight: '600',
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 24,
    minWidth: 30,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#F44336',
    fontSize: 12,
    fontWeight: '600',
  },
  percentageInputEdit: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
    textAlign: 'center',
    minWidth: 150,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E8E8E8',
    borderRadius: 4,
    padding: 16,
  },
  editActionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginLeft: 12,
  },
  // Category Tab Styles
  categoryDescription: {
    fontSize: 12,
    color: '#666',
    marginBottom: 16,
    lineHeight: 20,
  },
  categoryTagsContainer: {
    // marginBottom: 20,
  },
  categoryTagsContent: {
    // paddingHorizontal: 16,
    gap: 12,
  },
  categoryTag: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    height: 30,
  },
  categoryTagSelected: {
    backgroundColor: ColorThemes.light.primary_background,
  },
  categoryTagText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '400',
  },
  categoryTagTextSelected: {
    color: ColorThemes.light.primary_main_color,
    // fontWeight: '600',
  },
  categoryTableContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    marginTop: 16,
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: 12,
    // paddingHorizontal: 16,
  },
  tableHeaderText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
    textAlign: 'center',
  },
  tableHeaderFirstColumn: {
    flex: 2,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    paddingHorizontal: 8,
  },
  tableHeaderColumn: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  tableRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    // paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  tableFirstColumn: {
    flex: 2,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    paddingHorizontal: 8,
  },
  tableRowLabel: {
    fontSize: 14,
    color: '#333',
    fontWeight: '400',
    textAlign: 'center',
  },
  tableCell: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: 100,
  },
  tableCellText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  tableCellInput: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
    textAlign: 'center',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E8E8E8',
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 60,
  },
  tableActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginLeft: 12,
  },
  editCellContainer: {
    alignItems: 'center',
    gap: 8,
  },
  cellActions: {
    flexDirection: 'row',
    gap: 4,
    marginTop: 4,
  },
  cellContainer: {
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 40,
  },
  childCategoriesContainer: {
    marginTop: 20,
    gap: 16,
  },
});

export default ConfigAffiliate;
