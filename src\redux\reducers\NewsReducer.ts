import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {fetchNews, loadMoreNews} from '../actions/newsAction';
import {NewsItem} from '../models/news';

interface NewsStoreState {
  data: Array<NewsItem>;
  totalCount: number;
  loading?: boolean;
  loadingMore?: boolean;
}

export type {NewsStoreState};

const initState: NewsStoreState = {
  data: [],
  totalCount: 0,
  loading: false,
  loadingMore: false,
};

export const newsSlice = createSlice({
  name: 'news',
  initialState: initState,
  reducers: {
    setData: <K extends keyof NewsStoreState>(
      state: NewsStoreState,
      action: PayloadAction<{
        stateName: K;
        data: NewsStoreState[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchNews.pending, state => {
        state.loading = true;
      })
      .addCase(fetchNews.fulfilled, (state, action: PayloadAction<any>) => {
        state.loading = false;
        state.data = action.payload.data;
        state.totalCount = action.payload.totalCount;
      })
      .addCase(fetchNews.rejected, state => {
        state.loading = false;
      })
      .addCase(loadMoreNews.pending, state => {
        state.loadingMore = true;
      })
      .addCase(
        loadMoreNews.fulfilled,
        (state, action: PayloadAction<NewsItem[]>) => {
          state.loadingMore = false;
          state.data = [...state.data, ...action.payload];
        },
      )
      .addCase(loadMoreNews.rejected, state => {
        state.loadingMore = false;
      });
  },
});

export const {setData} = newsSlice.actions;

export default newsSlice.reducer;
