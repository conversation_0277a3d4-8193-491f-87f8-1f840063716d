import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, ScrollView } from 'react-native';
import { AppButton } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';
import { useFocusEffect } from '@react-navigation/native';

interface Step4VerificationCodeProps {
  onComplete: (code: string) => void;
  isLoading: boolean;
}

const Step4VerificationCode: React.FC<Step4VerificationCodeProps> = ({
  onComplete,
  isLoading,
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [countdown, setCountdown] = useState(60);
  const inputsRef = useRef<(TextInput | null)[]>([]);
  
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleChange = (text: string, index: number) => {
    if (text.length > 1) {
      // Handle paste
      const pastedCode = text.slice(0, 6);
      const newOtp = [...otp];
      for (let i = 0; i < pastedCode.length && i < 6; i++) {
        newOtp[i] = pastedCode[i];
      }
      setOtp(newOtp);
      
      // Focus on the next empty input or the last one
      const nextIndex = Math.min(pastedCode.length, 5);
      inputsRef.current[nextIndex]?.focus();
      return;
    }

    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Auto focus next input
    if (text && index < 5) {
      inputsRef.current[index + 1]?.focus();
    }
  };

  const handleBackspace = (index: number) => {
    const newOtp = [...otp];
    if (newOtp[index]) {
      newOtp[index] = '';
      setOtp(newOtp);
    } else if (index > 0) {
      newOtp[index - 1] = '';
      setOtp(newOtp);
      inputsRef.current[index - 1]?.focus();
    }
  };

  const handleComplete = () => {
    const code = otp.join('');
    onComplete(code);
  };

  const isCodeComplete = otp.every(digit => digit !== '') && !isLoading;

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {/* Title */}
          <View style={styles.titleContainer}>
            <Text style={styles.title}>Enter Verification Code</Text>
          </View>

          {/* Description */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionText}>
              Nhập mã xác minh hiện thi trong ứng dụng xác thực của bạn.
            </Text>
          </View>

          {/* OTP Input */}
          <View style={styles.otpContainer}>
            {otp.map((digit, index) => (
              <TextInput
                key={index}
                ref={el => {
                  inputsRef.current[index] = el;
                }}
                style={[
                  styles.otpInput,
                  digit ? styles.otpInputFilled : null,
                ]}
                value={digit}
                onChangeText={text => handleChange(text, index)}
                onKeyPress={({ nativeEvent }) => {
                  if (nativeEvent.key === 'Backspace') {
                    handleBackspace(index);
                  }
                }}
                keyboardType="numeric"
                maxLength={1}
                textContentType="oneTimeCode"
                autoFocus={index === 0}
              />
            ))}
          </View>

          {/* Countdown */}
          <View style={styles.countdownContainer}>
            <Text style={styles.countdownText}>
              Please wait {countdown} seconds before you can resend the code.
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Button */}
      <View style={styles.buttonContainer}>
        <AppButton
          title="Xác nhận"
          onPress={()=>{
            //clear inputs
            setOtp(['', '', '', '', '', '']);
            setCountdown(60);
            handleComplete();
          }}
          disabled={!isCodeComplete}
          backgroundColor={
            isCodeComplete
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.neutral_lighter_border_color
          }
          containerStyle={styles.button}
          textStyle={{
            ...styles.buttonText,
            color: isCodeComplete ? ColorThemes.light.white : ColorThemes.light.neutral_text_subtitle_color,
          }}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    paddingTop: 20,
    alignItems: 'center',
  },
  titleContainer: {
    marginBottom: 16,
  },
  title: {
    ...TypoSkin.heading4,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
    textAlign: 'center',
  },
  descriptionContainer: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  descriptionText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_body_color,
    textAlign: 'center',
    lineHeight: 20,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 32,
  },
  otpInput: {
    width: 40,
    height: 40,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
    textAlign: 'center',
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    
  },
  otpInputFilled: {
    borderColor: ColorThemes.light.primary_main_color,
    backgroundColor: ColorThemes.light.primary_background,
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: 16,
  },
  countdownContainer: {
    paddingHorizontal: 20,
  },
  countdownText: {
    ...TypoSkin.regular1,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
  buttonContainer: {
    paddingBottom: 20,
  },
  button: {
    height: 48,
    borderRadius: 24,
    borderWidth: 0,
  },
  buttonText: {
    ...TypoSkin.buttonText1,
    fontWeight: '600',    
  },
});

export default Step4VerificationCode;
