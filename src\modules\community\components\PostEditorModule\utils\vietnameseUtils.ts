// Kiểm tra xem có phải đang composing không (nhập tiếng Việt)
export const isVietnameseComposing = (
  text: string,
  position: number,
): boolean => {
  // Kiểm tra cả trường hợp đang nhập ký tự đầu tiên
  if (position <= text.length) {
    // Kiểm tra 15 ký tự cuối cùng có chứa dấu tiếng Việt không
    const lastChars = text.slice(Math.max(0, position - 15), position);
    // Bao gồm cả chữ cái hoa có dấu
    return /[áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ]/.test(
      lastChars,
    );
  }
  return false;
};

// Hàm kiểm tra xem một ký tự có phải là ký tự Latin cơ bản không dấu
export const isBasicLatinChar = (char: string): boolean => {
  return /^[a-zA-Z]$/.test(char);
};

// Hàm kiểm tra xem một ký tự có phải là ký tự tiếng Việt có dấu
export const isVietnameseChar = (char: string): boolean => {
  return /^[áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ]$/.test(
    char,
  );
};

// Hàm kiểm tra xem một ký tự tiếng Việt có dấu có thể thay thế cho ký tự Latin không dấu
export const canReplaceLatinChar = (
  latinChar: string,
  vietnameseChar: string,
): boolean => {
  const latinToVietnamese: {[key: string]: RegExp} = {
    a: /^[áàảãạăắằẳẵặâấầẩẫậ]$/i,
    e: /^[éèẻẽẹêếềểễệ]$/i,
    i: /^[íìỉĩị]$/i,
    o: /^[óòỏõọôốồổỗộơớờởỡợ]$/i,
    u: /^[úùủũụưứừửữự]$/i,
    y: /^[ýỳỷỹỵ]$/i,
    d: /^[đ]$/i,
  };

  const lowerLatinChar = latinChar.toLowerCase();
  if (latinToVietnamese[lowerLatinChar]) {
    return latinToVietnamese[lowerLatinChar].test(vietnameseChar);
  }
  return false;
};

// Kiểm tra xem có phải là ký tự tiếng Việt đầu tiên không
export const isFirstVietnameseChar = (text: string): boolean => {
  return (
    text.length === 1 &&
    /^[áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ]$/.test(
      text,
    )
  );
};

// Map cho việc kiểm tra thay thế ký tự
export const getVietnameseCharMap = (): {[key: string]: RegExp} => ({
  a: /^[áàảãạăắằẳẵặâấầẩẫậÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬ]$/,
  e: /^[éèẻẽẹêếềểễệÉÈẺẼẸÊẾỀỂỄỆ]$/,
  i: /^[íìỉĩịÍÌỈĨỊ]$/,
  o: /^[óòỏõọôốồổỗộơớờởỡợÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢ]$/,
  u: /^[úùủũụưứừửữựÚÙỦŨỤƯỨỪỬỮỰ]$/,
  y: /^[ýỳỷỹỵÝỲỶỸỴ]$/,
  d: /^[đĐ]$/,
});
