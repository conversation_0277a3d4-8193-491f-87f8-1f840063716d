import {useNavigation} from '@react-navigation/native';
import React, {FC} from 'react';
import {
  Image,
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {NewsEvent} from '../../../redux/models/newsEvent';
import {RootScreen} from '../../../router/router';
import {checkTimeWithNow, formatTimestamp} from '../../../utils/Utils';
import {CountdownTimer} from '../../news/components/TabEventComponents/components/CountdownTimer';
import {IconText} from '../../news/components/TabEventComponents/components/IconText';

interface CardInfoEventProps {
  item: NewsEvent;
  onRegisterDone?: (id: string) => void;
}

const defaultImage = require('../../../assets/images/default_img.png');

export const CardInfoEvent: FC<CardInfoEventProps> = ({item}) => {
  const navigation = useNavigation<any>();

  const navigateToEventDetail = (event: NewsEvent) => {
    navigation.navigate(RootScreen.DetailNews, {
      item: event,
    });
  };

  return (
    <TouchableOpacity
      style={styles.vCard}
      onPress={() => navigateToEventDetail(item)}>
      {checkTimeWithNow(item.DateStart) ? (
        <ImageBackground
          source={item.Img ? {uri: item.Img} : defaultImage}
          style={styles.vCardImage}>
          <CountdownTimer TargetDate={item.DateStart} />
        </ImageBackground>
      ) : (
        <Image
          source={item.Img ? {uri: item.Img} : defaultImage}
          style={styles.vCardImage}
        />
      )}
      <View style={styles.vCardContent}>
        <Text style={[styles.vCardTitle]}>{item.Title}</Text>
        <IconText
          icon={require('../../../assets/icons/pinmap.png')}
          text={item.Address}
        />
        <IconText
          icon={require('../../../assets/icons/calendar.png')}
          text={formatTimestamp(item.DateStart)}
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  vCard: {
    height: 140,
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
    flexDirection: 'row',
    padding: 10,
  },
  vCardImage: {
    width: 140,
    height: 120,
    borderRadius: 8,
    marginRight: 12,
  },
  vCardContent: {flex: 1, justifyContent: 'center'},
  vCardTitle: {
    fontSize: 16,
    marginBottom: 8,
    color: ColorThemes.light.infor_text_color,
  },
});
