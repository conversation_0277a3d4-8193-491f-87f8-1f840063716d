import { FlatList, Image, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from "react-native";
import { ColorThemes } from "../../assets/skin/colors";
import { InforHeader } from "../../Screen/Layout/headers/inforHeader";
import { hideBottomSheet, Winicon } from "wini-mobile-components";
import { useState } from "react";
import { LoadingUI } from "../../features/loading";
import LinearGradient from "react-native-linear-gradient";
import ConfigAPI from "../../Config/ConfigAPI";
import { Ultis } from "../../utils/Utils";
import { TypoSkin } from "../../assets/skin/typography";
import { RootScreen } from "../../router/router";
import { useNavigation, useRoute } from "@react-navigation/native";
import EmptyPage from "../../Screen/emptyPage";

const SearchAffiliate = ({ members, customerBottomSheetRef }: { members: any, customerBottomSheetRef: any }) => {
    const [loading, setLoading] = useState(false);
    const navigation = useNavigation<any>();
    //router params members
    const route = useRoute<any>();
    const [data, setData] = useState<any>([]);
    const [isSearching, setIsSearching] = useState(false);

    //params search
    const [search, setSearch] = useState<string>('');

    const handleItemClick = (item: any) => {
        navigation.push(RootScreen.TreeAffiliateDetail, {
            customerId: item.id,
            username: item.name,
        });
    };
    const renderCustomerItem = ({ item }: { item: any }) => {

        return (
            <TouchableOpacity
                style={[styles.customerItem]}
                onPress={() => { 
                    hideBottomSheet(customerBottomSheetRef);
                    navigation.push(RootScreen.TreeAffiliateDetail, {
                        customerId: item.Id,
                        username: item.Name,
                    });
                }}
                activeOpacity={0.7}>
                <View style={styles.avatarContainer}>
                    {item.AvatarUrl ? (
                        <Image
                            source={{ uri: ConfigAPI.urlImg + item.AvatarUrl }}
                            style={{ width: 40, height: 40, borderRadius: 20 }}
                            resizeMode="cover"
                        />
                    ) : (
                        <Winicon
                            src="fill/users/profile"
                            size={24}
                            color={ColorThemes.light.primary_main_color}
                        />
                    )}
                </View>
                <View style={styles.customerInfo}>
                    <Text
                        style={[
                            styles.customerName,
                        ]}>
                        {item.Name}
                    </Text>
                    {item.Mobile && (
                        <Text
                            style={[
                                styles.customerPhone,
                            ]}>
                            {item.Mobile}
                        </Text>
                    )}
                </View>
            </TouchableOpacity>
        );
    };
    const handleSearch = (text: string) => {
        setSearch(text);
        if(!text) {
            setData([]);
            return;
        };
        setData(members.filter((item: any) =>
            item.Name.toLowerCase().includes(text.toLowerCase() || item.Mobile.toLowerCase().includes(text.toLowerCase())),
        ),
        );
    };
    return (<View style={styles.container}>
        {/* <InforHeader title="Tìm kiếm thành viên" /> */}
        <View style={styles.searchContainer}>
            <View style={styles.searchInputContainer}>
                <Winicon
                    src="outline/user interface/search"
                    size={16}
                    color={ColorThemes.light.neutral_text_subtitle_color}
                />
                <TextInput
                    style={styles.searchInput}
                    placeholder="Tìm kiếm theo tên hoặc số điện thoại"
                    value={search}
                    onChangeText={handleSearch}
                    placeholderTextColor={ColorThemes.light.neutral_text_subtitle_color}
                />
            </View>
        </View>
        <View style={styles.content}>

            {loading ? (
                <LoadingUI isLoading={loading} />
            ) : (
                <FlatList
                    data={data}
                    renderItem={renderCustomerItem}
                    keyExtractor={item => item.Id}

                    onEndReachedThreshold={0.5}
                    ListEmptyComponent={() => <EmptyPage title="Không tìm thấy thành viên" />}
                    showsVerticalScrollIndicator={false}
                    style={styles.list}
                />
            )}
        </View>
    </View>);
};

export default SearchAffiliate;


const styles = StyleSheet.create({
    container: {
        width: '100%',
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        maxHeight: '100%',
        minHeight: 700,
    },
    list: {
        flex: 1,
    },
    backgroundGradient: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: 200,
        backgroundColor: ColorThemes.light.primary_main_color,
        borderBottomLeftRadius: 30,
        borderBottomRightRadius: 30,
    },
    safeArea: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'transparent',
    },
    backButton: {
        padding: 8,
    },
    headerCenter: {
        flex: 1,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        gap: 8,
    },
    logoContainer: {
        alignItems: 'center',
    },
    logo: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        alignItems: 'center',
        justifyContent: 'center',
    },
    logoText: {
        fontSize: 16,
    },
    headerTitle: {
        ...TypoSkin.heading6,
        color: ColorThemes.light.neutral_absolute_background_color,
        fontWeight: '600',
    },
    searchButton: {
        padding: 8,
    },
    content: {
        flex: 1,
        backgroundColor: ColorThemes.light.white,
        maxHeight: '100%',
        minHeight: 600,
    },
    userStatsContainer: {
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        paddingHorizontal: 16,
        paddingVertical: 20,
        marginBottom: 16,
    },
    userName: {
        ...TypoSkin.heading5,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '600',
        marginBottom: 8,
        textAlign: 'left',
    },
    statsRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
    },
    statItem: {
        alignItems: 'center',
        flex: 1,
    },
    statLabel: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginBottom: 8,
        textAlign: 'center',
    },
    statValue: {
        ...TypoSkin.heading5,
        fontWeight: '700',
    },
    affiliateList: {
        paddingHorizontal: 16,
        gap: 12,
    },
    affiliateItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 16,
        paddingHorizontal: 12,
        paddingVertical: 12,
    },
    affiliateLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        gap: 12,
    },
    levelBadge: {
        width: 40,
        height: 40,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    levelText: {
        ...TypoSkin.medium1,
        color: ColorThemes.light.neutral_absolute_background_color,
        fontWeight: '600',
    },
    affiliateInfo: {
        flex: 1,
        gap: 8,
    },
    nameRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    affiliateName: {
        ...TypoSkin.medium1,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '500',
    },
    detailsRow: {
        flexDirection: 'row',
        gap: 16,
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    detailText: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
    },
    expandButton: {
        padding: 8,
    },
    // Empty State Styles
    emptyContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
        paddingHorizontal: 20,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        borderRadius: 12,
        marginHorizontal: 16,
    },
    emptyText: {
        ...TypoSkin.heading6,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '600',
        marginTop: 16,
        textAlign: 'center',
    },
    emptySubText: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginTop: 8,
        textAlign: 'center',
    },
    searchContainer: {
        paddingHorizontal: 16,
        paddingVertical: 12,
    },
    searchInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: ColorThemes.light.neutral_main_background_color,
        borderRadius: 8,
        paddingHorizontal: 12,
        gap: 8,
    },
    searchInput: {
        flex: 1,
        paddingVertical: 12,
        fontSize: 14,
        color: ColorThemes.light.neutral_text_title_color,
    },
    customerItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        gap: 12,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.neutral_main_border_color,
    },
    avatarContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: ColorThemes.light.primary_background,
        alignItems: 'center',
        justifyContent: 'center',
    },
    customerInfo: {
        flex: 1,
    },
    customerName: {
        ...TypoSkin.regular2,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '600',
    },
    customerPhone: {
        ...TypoSkin.regular3,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginTop: 2,
        fontSize: 12,
    },
});
