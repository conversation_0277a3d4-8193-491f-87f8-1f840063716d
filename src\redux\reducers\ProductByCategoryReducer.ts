import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {Category} from '../models/category';
import {AppDispatch} from '../store/store';
import {useDispatch} from 'react-redux';
import {ActiveFilters} from '../../modules/Product/Popup/PopupFilterProductComponent/types';

interface CategoryStoreState {
  parentCategory: Category | null;
  childrenCategory: Array<Category>;
  currentCategory: Category | null;
  newCategoryId: string | null;
  loading: boolean;
  filter: {
    categoryId: string | null;
    brandId: string | null;
    activeFilters: ActiveFilters;
    sortOption: string | null;
    maxPrice: number | null;
    textSearch: string | null;
  };
}

export type {CategoryStoreState};

const initState: CategoryStoreState = {
  parentCategory: null,
  childrenCategory: [],
  currentCategory: null,
  newCategoryId: null,
  loading: false,
  filter: {
    categoryId: null,
    brandId: null,
    activeFilters: {},
    sortOption: null,
    maxPrice: null,
    textSearch: null,
  },
};

export const productByCategorySlice = createSlice({
  name: 'productByCategory',
  initialState: initState,
  reducers: {
    setData: <K extends keyof CategoryStoreState>(
      state: CategoryStoreState,
      action: PayloadAction<{
        stateName: K;
        data: CategoryStoreState[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
    resetData: (state: CategoryStoreState) => {
      state.parentCategory = null;
      state.childrenCategory = [];
      state.currentCategory = null;
      state.loading = false;
    },
  },
});

export const {setData, resetData} = productByCategorySlice.actions;

export default productByCategorySlice.reducer;

export const useProductByCategoryHook = () => {
  const dispatch = useDispatch<AppDispatch>();

  const action = {
    setData: (
      stateName: keyof CategoryStoreState,
      data: CategoryStoreState[keyof CategoryStoreState],
    ) => {
      dispatch(setData({stateName, data}));
    },
    resetData: () => {
      dispatch(resetData());
    },
  };

  return action;
};
