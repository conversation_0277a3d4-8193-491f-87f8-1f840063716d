import React, {use, useEffect, useState} from 'react';
import {
  View,
  Image,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import {FlatList, ScrollView} from 'react-native-gesture-handler';
import {FLoading, Winicon} from 'wini-mobile-components';
import {TypeMenuReview} from '../../../Config/Contanst';
import {ReviewData, ReviewOrderData} from '../../../mock/shopData';
import {ReviewDataDto, ReviewProductProps} from '../../dto/dto';
import ReviewProductIteCard from '../card/CardReviewItem';
import {DataController} from '../../../base/baseController';
import {useSelectorShopState} from '../../../redux/hook/shopHook ';
import EmptyPage from '../../../Screen/emptyPage';

const ReviewProduct = (props: ReviewProductProps) => {
  const {type} = props;
  const [data, setData] = useState<ReviewDataDto[] | any[]>([]);
  const RatingController = new DataController('Rating');
  const shopInfo = useSelectorShopState().data;
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    setIsLoading(true);
    if (type == TypeMenuReview.Product) {
      callApiGetReview(2);
    } else {
      callApiGetReview(1);
    }
  }, [type]);

  let callApiGetReview = async (Type: number) => {
    if (Type == 2) {
      let resRating = await RatingController.getPatternList({
        query: `@ShopId:  {${shopInfo[0].Id}} @Type: [${Type}]`,
        pattern: {
          CustomerId: ['Id', 'Name', 'AvatarUrl'],
          ProductId: ['Id', 'Name', 'Description', 'Img', 'Content'],
        },
      });
      if (resRating && resRating.code === 200) {
        console.log('check-resRating', resRating);
        let arrayData = resRating?.data?.map((item: any) => {
          return {
            ...item,
            Customer: resRating?.Customer?.find(
              (customer: any) => customer.Id == item.CustomerId,
            ),
            Product: resRating?.Product?.find(
              (product: any) => product.Id == item.ProductId,
            ),
          };
        });
        setData(arrayData);
        setIsLoading(false);
      }
    } else {
      let resRating = await RatingController.getPatternList({
        query: `@ShopId:  {${shopInfo[0].Id}} @Type: [${Type}]`,
        pattern: {
          CustomerId: ['Id', 'Name', 'AvatarUrl'],
        },
      });
      let arrayData = resRating?.data?.map((item: any) => {
        return {
          ...item,
          Customer: resRating?.Customer?.find(
            (customer: any) => customer.Id == item.CustomerId,
          ),
        };
      });
      setData(arrayData);
      setIsLoading(false);
    }
  };

  if (data && data.length > 0) {
    return (
      <FlatList
        data={data}
        style={{flex: 1}}
        keyExtractor={(item, i) => `${i} ${item.Id}`}
        renderItem={({item, index}) =>
          ReviewProductIteCard({item, index}, type as string, isLoading)
        }
      />
    );
  } else {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <EmptyPage />
      </View>
    );
  }
};

export default ReviewProduct;
