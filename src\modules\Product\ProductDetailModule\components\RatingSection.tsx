import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {Rating} from 'wini-mobile-components';
import {RatingSectionProps} from '../types';
import {ColorThemes} from '../../../../assets/skin/colors';
import {commonStyles} from '../styles';

const RatingSection: React.FC<RatingSectionProps> = ({
  rating = 0,
  onViewAllPress,
}) => {
  return (
    <View style={styles.ratingContainer}>
      <Rating
        value={rating ? Number(rating) : 0}
        size={20}
        fillColor="#FFC043"
      />
      <Text style={styles.ratingText}>
        {rating ? Number(rating).toFixed(1) : '0.0'}
      </Text>
      <View style={commonStyles.flexGrow} />
      <TouchableOpacity onPress={onViewAllPress}>
        <Text style={styles.viewAllText}>Xem tất cả</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  ratingText: {
    fontSize: 14,
    color: '#000',
    marginLeft: 8,
  },
  viewAllText: {
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
    marginLeft: 'auto',
  },
});

export default RatingSection;
