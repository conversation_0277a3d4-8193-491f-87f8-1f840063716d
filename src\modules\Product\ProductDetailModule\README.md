# ProductDetailModule

Module này chứa tất cả các component, hook và logic liên quan đến trang chi tiết sản phẩm.

## Cấu trúc thư mục

```
ProductDetailModule/
├── index.ts                   # Export tất cả components, types, hooks
├── components/               # Các React components
│   ├── skeleton/            # Skeleton loading components
│   │   ├── SkeletonBox.tsx
│   │   └── ProductDetailSkeleton.tsx
│   ├── ProductDetailHeader.tsx
│   ├── ProductImageCarousel.tsx
│   ├── ProductPriceSection.tsx
│   ├── ProductInfoSection.tsx
│   ├── ShippingSection.tsx
│   ├── RatingSection.tsx
│   ├── SellerInfoSection.tsx
│   ├── ShopInfoSection.tsx
│   ├── RelatedProductsSection.tsx
│   ├── ProductDescriptionSection.tsx
│   └── BottomActionBar.tsx
├── types/                    # TypeScript interfaces
│   └── index.ts
├── hooks/                    # Custom React hooks
│   └── useProductDetail.ts
└── styles/                   # Shared styles
    └── index.ts
```

## Components

### Skeleton Components

- **SkeletonBox**: Component cơ bản để tạo skeleton loading
- **ProductDetailSkeleton**: Skeleton loading cho toàn bộ trang

### Main Components

- **ProductDetailHeader**: Header với nút back, cart và menu
- **ProductImageCarousel**: Carousel hiển thị ảnh sản phẩm
- **ProductPriceSection**: Hiển thị giá và các action buttons (like, share)
- **ProductInfoSection**: Thông tin sản phẩm (brand, sold, stock)
- **ShippingSection**: Thông tin vận chuyển
- **RatingSection**: Hiển thị đánh giá
- **SellerInfoSection**: Thông tin người bán
- **ShopInfoSection**: Thống kê của shop
- **RelatedProductsSection**: Sản phẩm liên quan từ shop
- **ProductDescriptionSection**: Mô tả sản phẩm
- **BottomActionBar**: Thanh action bottom (chat, add to cart, buy now)

## Hooks

### useProductDetail

Custom hook quản lý toàn bộ logic lấy dữ liệu chi tiết sản phẩm:

- Lấy thông tin sản phẩm
- Lấy thông tin shop
- Xử lý favorite
- Quản lý loading/refreshing states

## Sử dụng

```tsx
import {
  ProductDetailSkeleton,
  ProductDetailHeader,
  ProductImageCarousel,
  // ... các component khác
  useProductDetail,
} from './ProductDetailModule';

export default function ProductDetail() {
  const {id} = route.params;

  // Sử dụng custom hook
  const {
    loading,
    refreshing,
    data,
    shop,
    productImages,
    like,
    setLike,
    onRefresh,
  } = useProductDetail(id);

  if (loading) {
    return <ProductDetailSkeleton />;
  }

  return (
    <SafeAreaView>
      <ProductDetailHeader {...} />
      <ScrollView>
        <ProductImageCarousel images={productImages} />
        {/* ... các component khác */}
      </ScrollView>
      <BottomActionBar {...} />
    </SafeAreaView>
  );
}
```

## Lợi ích của việc module hóa

1. **Tổ chức code tốt hơn**: Mỗi component có trách nhiệm riêng
2. **Dễ maintain**: Dễ dàng tìm và sửa đổi từng phần
3. **Reusable**: Các component có thể được sử dụng lại ở nơi khác
4. **Testing**: Dễ dàng viết unit test cho từng component
5. **Performance**: Có thể optimize từng component riêng biệt
