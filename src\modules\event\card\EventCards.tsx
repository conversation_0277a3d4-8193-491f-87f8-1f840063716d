import React, {FC, useState} from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  ImageBackground,
  Linking,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';

import {CountdownTimer} from '../../news/components/TabEventComponents/components/CountdownTimer';
import BasePopupConfirm from '../../../components/Popup/BasePopupConfirm';
import {ColorThemes} from '../../../assets/skin/colors';
import {NewsEvent} from '../../../redux/models/newsEvent';
import {eventRegisterAction} from '../../../redux/actions/eventRegisterAction';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {RootScreen} from '../../../router/router';
import {checkTimeWithNow} from '../../../utils/Utils';
import {checkTimeStatus} from '../../../utils/timeUltis';
import RegisteredChip from '../chip/RegisteredChip';

const defaultImage = require('../../../assets/images/default_img.png');

interface EventCardProps {
  item: NewsEvent;
  onRegisterDone?: (id: string) => void;
}

export const StatusChipEvent: FC<{
  item: NewsEvent;
  onRegister: () => void;
  onJoin: () => void;
}> = ({item, onRegister, onJoin}) => {
  if (item.isRegistered) return <RegisteredChip />;

  const status = checkTimeStatus(item.DateStart, item.DateEnd);

  switch (status) {
    case 'started':
      return (
        <TouchableOpacity
          style={[
            styles.actionButton,
            {backgroundColor: ColorThemes.light.primary_darker_color},
          ]}
          onPress={onRegister}>
          <Text style={styles.actionButtonText}>Đăng ký</Text>
        </TouchableOpacity>
      );

    case 'onGoing':
      return (
        <TouchableOpacity
          style={[
            styles.actionButton,
            {backgroundColor: ColorThemes.light.secondary5_sub_color},
          ]}
          onPress={onJoin}>
          <Text style={styles.actionButtonText}>Tham gia</Text>
        </TouchableOpacity>
      );

    case 'ended':
      return (
        <View
          style={[
            styles.registeredChip,
            {backgroundColor: ColorThemes.light.error_darker_color},
          ]}>
          <Text style={styles.registeredText}>Đã kết thúc</Text>
        </View>
      );

    default:
      return null;
  }
};

export const EventCardHorizontal: FC<EventCardProps> = ({
  item,
  onRegisterDone,
}) => {
  const navigation = useNavigation<any>();
  const [showConfirm, setShowConfirm] = useState(false);
  const [loading, setLoading] = useState(false);
  const customerHook = useSelectorCustomerState();

  // hiển thị popup xác nhận đăng ký sự kiện
  const handleShowConfirm = () => {
    setShowConfirm(true);
  };

  const handleCancel = () => {
    setShowConfirm(false);
  };

  // đăng ký sự kiện
  const handleConfirm = async () => {
    try {
      setLoading(true);
      const customerId = customerHook.data?.Id;

      if (!customerId) {
        console.error('Customer ID not found');
        return;
      }

      const dataCreate = {
        Status: 'registered',
        NewsEventId: item.Id,
        CustomerId: customerId,
      };

      await eventRegisterAction.create(dataCreate);
      onRegisterDone?.(item.Id);
      setShowConfirm(false);
    } catch (error) {
      console.error('Error registering for event:', error);
    } finally {
      setLoading(false);
    }
  };

  // mở link sự kiện
  const handleJoinEvent = () => {
    if (item.EventLink) Linking.openURL(item.EventLink);
  };

  // chuyển đến trang chi tiết sự kiện
  const navigateToEventDetail = () => {
    navigation.navigate(RootScreen.DetailEvent, {
      id: item.Id,
    });
  };

  // render ảnh và countdown timer
  const renderEventImage = () => {
    const imageSource = item.Img ? {uri: item.Img} : defaultImage;

    if (checkTimeWithNow(item.DateStart)) {
      return (
        <ImageBackground source={imageSource} style={styles.cardImage}>
          <CountdownTimer TargetDate={item.DateStart} textSize="large" />
        </ImageBackground>
      );
    }

    return <Image source={imageSource} style={styles.cardImage} />;
  };

  return (
    <>
      <TouchableOpacity style={styles.card} onPress={navigateToEventDetail}>
        {renderEventImage()}

        <View style={styles.cardContent}>
          <Text style={styles.cardTitle} numberOfLines={2}>
            {item.Title}
          </Text>

          <View style={styles.cardFooter}>
            <Text
              style={[
                styles.cardLocation,
                {width: item.isRegistered ? '50%' : '60%'},
              ]}
              numberOfLines={1}>
              {item.Address}
            </Text>

            <StatusChipEvent
              item={item}
              onRegister={handleShowConfirm}
              onJoin={handleJoinEvent}
            />
          </View>
        </View>
      </TouchableOpacity>

      <BasePopupConfirm
        visible={showConfirm}
        loading={loading}
        title="Xác nhận đăng ký"
        message="Xác nhận đăng ký tham gia sự kiện này"
        onCancel={handleCancel}
        onConfirm={handleConfirm}
      />
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    width: 280,
    backgroundColor: 'white',
    borderRadius: 12,
    marginRight: 16,
    elevation: 1,
  },
  cardImage: {
    width: '100%',
    height: 140,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderRadius: 8,
  },
  cardContent: {
    padding: 12,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: ColorThemes.light.infor_text_color,
  },
  cardLocation: {
    fontSize: 14,
    color: ColorThemes.light.infor_text_color,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  actionButton: {
    height: 26,
    width: 100,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonText: {
    color: ColorThemes.light.white,
    fontSize: 14,
  },
  registeredChip: {
    height: 26,
    backgroundColor: ColorThemes.light.secondary2_sub_color,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
  checkIcon: {
    width: 16,
    height: 16,
    backgroundColor: ColorThemes.light.secondary6_darker_color,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 6,
  },
  checkText: {
    color: ColorThemes.light.white,
    fontSize: 8,
    fontWeight: 'bold',
  },
  registeredText: {
    color: ColorThemes.light.white,
    fontSize: 14,
    fontWeight: '500',
  },
});
