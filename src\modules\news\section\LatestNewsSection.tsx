import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
} from 'react-native';

import LatestNewsCard from '../card/LatestNewsCard';
import {ColorThemes} from '../../../assets/skin/colors';
import {NewsItem} from '../../../redux/models/news';
import {newsAction} from '../../../redux/actions/newsAction';
import {RootScreen} from '../../../router/router';
import {useNavigation} from '@react-navigation/native';
import {TypoSkin} from '../../../assets/skin/typography';

// Component chính của ứng dụng
const LatestNewsSection = ({
  isRefresh,
  isLoadMore,
  onLoadMoreEnd,
}: {
  isRefresh: boolean;
  isLoadMore: boolean;
  onLoadMoreEnd: (hasMore: boolean) => void;
}) => {
  const [latestNews, setLatestNews] = useState<NewsItem[]>([]);
  const [page, setPage] = useState(1);
  const navigation = useNavigation<any>();
  const size = 5;

  useEffect(() => {
    if (isRefresh) {
      initData();
    } else {
      initData();
    }
  }, [isRefresh]);

  useEffect(() => {
    if (isLoadMore) {
      loadMoreData();
    }
  }, [isLoadMore]);

  const initData = async () => {
    const response = await newsAction.fetch({
      page: 1,
      size: size,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (response.data.length > 0) {
      setLatestNews(response.data);
      setPage(1);
    }
  };

  const loadMoreData = async () => {
    const nextPage = page + 1;
    const response = await newsAction.fetch({
      page: nextPage,
      size: size,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });

    if (response?.data?.length > 0) {
      setLatestNews(prevNews => [...prevNews, ...response.data]);
      setPage(nextPage);
      onLoadMoreEnd(true);
    } else {
      onLoadMoreEnd(false);
    }
  };

  const onSeeMore = () => {
    navigation.navigate(RootScreen.NewsScreen);
  };

  const onNavigateToDetail = (item: NewsItem) => {
    navigation.navigate(RootScreen.DetailNews, {id: item.Id});
  };

  return (
    <View>
      {/* Section Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Tin mới</Text>
        <TouchableOpacity onPress={onSeeMore}>
          <Text style={styles.seeMore}>Xem thêm</Text>
        </TouchableOpacity>
      </View>

      {/* News List */}
      <FlatList
        data={latestNews}
        renderItem={({item}) => (
          <View style={{marginBottom: 12}}>
            <LatestNewsCard
              item={item}
              onPress={() => onNavigateToDetail(item)}
            />
          </View>
        )}
        keyExtractor={item => item.Id}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
      />
      {isLoadMore && (
        <ActivityIndicator
          style={{marginVertical: 10}}
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      )}
    </View>
  );
};

// StyleSheet để định nghĩa toàn bộ style
const styles = StyleSheet.create({
  // Header Styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 12,
  },
  headerTitle: {
    ...TypoSkin.title1,
    color: ColorThemes.light.neutral_text_title_color,
  },
  seeMore: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.infor_main_color,
  },
});

export default LatestNewsSection;
