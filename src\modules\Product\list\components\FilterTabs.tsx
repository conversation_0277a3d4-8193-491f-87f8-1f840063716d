import React from 'react';
import {ScrollView, StyleSheet, Text, TouchableOpacity} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';

const filterTabs = [
  {id: 'hot', name: 'HOT', icon: 'fill/development/shape-star'},
  {id: 'freeship', name: 'Freeship', icon: 'fill/shopping/delivery'},
  {id: 'new', name: '<PERSON>ớ<PERSON>', icon: 'fill/arrows/reload'},
  {id: 'discount', name: '<PERSON><PERSON>ận hàng ưu đãi', icon: 'fill/shopping/tag'},
];

interface FilterTabsProps {
  selectedFilter: string;
  onSelectFilter: (id: string) => void;
}

const FilterTabs = ({selectedFilter, onSelectFilter}: FilterTabsProps) => (
  <ScrollView
    horizontal
    showsHorizontalScrollIndicator={false}
    style={styles.filterTabsContainer}
    contentContainerStyle={styles.filterTabsContent}>
    {filterTabs.map(tab => (
      <TouchableOpacity
        key={tab.id}
        style={[
          styles.filterTab,
          selectedFilter === tab.id && styles.activeFilterTab,
        ]}
        onPress={() => onSelectFilter(tab.id)}>
        <Winicon
          src={tab.icon}
          size={16}
          color={selectedFilter === tab.id ? '#FFFFFF' : '#666666'}
        />
        <Text
          style={[
            styles.filterTabText,
            selectedFilter === tab.id && styles.activeFilterTabText,
          ]}>
          {tab.name}
        </Text>
      </TouchableOpacity>
    ))}
  </ScrollView>
);

const styles = StyleSheet.create({
  filterTabsContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    minHeight: 32,
    maxHeight: 32,
    marginTop: 32,
  },
  filterTabsContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  filterTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#F5F5F5',
    borderRadius: 24,
    gap: 6,
  },
  activeFilterTab: {
    backgroundColor: '#2962FF',
  },
  filterTabText: {
    ...TypoSkin.buttonText6,
  },
  activeFilterTabText: {
    color: ColorThemes.light.neutral_absolute_background_color,
  },
});

export default React.memo(FilterTabs);
