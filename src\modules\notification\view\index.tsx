import {View} from 'react-native';
import React, {useEffect} from 'react';
import NotificationListChip from '../components/NotificationListChip';
import NotificationList from '../components/NotificationList';
import {useDispatch} from 'react-redux';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import {
  fetchNotifications,
  readAllNotification,
} from '../../../redux/actions/notificationAction';

const NotificationIndex = () => {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(fetchNotifications({type: [1]}) as any);
    dispatch(readAllNotification([1]) as any);
  }, []);

  const handleChangeTab = (type: string) => {
    switch (type) {
      case 'general':
        dispatch(fetchNotifications({type: [1]}) as any);
        dispatch(readAllNotification([1]) as any);
        break;
      case 'affiliate':
        dispatch(fetchNotifications({type: [2, 3]}) as any);
        dispatch(readAllNotification([2, 3]) as any);
        break;
      default:
        break;
    }
  };

  return (
    <View style={{flex: 1}}>
      <InforHeader title={'Thông báo'} />

      <View style={{flex: 1}}>
        <View style={{paddingHorizontal: 16}}>
          <NotificationListChip onChange={handleChangeTab} />
        </View>

        <View style={{flex: 1}}>
          <NotificationList />
        </View>
      </View>
    </View>
  );
};

export default NotificationIndex;
