import React, {useCallback} from 'react';
import {View, Text, FlatList, StyleSheet} from 'react-native';
import SquareProductCard from '../../../../components/SquareProductCard';
import {RelatedProductsSectionProps, ProductData} from '../types';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ITEM_WIDTH, ITEM_SPACING} from '../styles';

const RelatedProductsSection: React.FC<RelatedProductsSectionProps> = ({
  products = [],
  onProductPress,
}) => {
  const renderProductItem = useCallback(
    ({item}: {item: ProductData}) => (
      <SquareProductCard
        item={item}
        onPress={() => onProductPress(item.Id)}
        width={ITEM_WIDTH}
        showRating={true}
      />
    ),
    [onProductPress],
  );

  const keyExtractor = useCallback(
    (item: ProductData) => `product-${item.Id}`,
    [],
  );

  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: ITEM_WIDTH,
      offset: (ITEM_WIDTH + ITEM_SPACING) * index,
      index,
    }),
    [],
  );

  if (!products || products.length === 0) {
    return null;
  }

  return (
    <View style={styles.otherProductsContainer}>
      <Text style={styles.otherProductsTitle}>Sản phẩm khác của shop</Text>
      <FlatList
        data={products}
        horizontal={true}
        snapToInterval={ITEM_WIDTH + ITEM_SPACING}
        snapToAlignment="start"
        decelerationRate="fast"
        pagingEnabled={false}
        disableIntervalMomentum={true}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.otherProductsList}
        keyExtractor={keyExtractor}
        renderItem={renderProductItem}
        getItemLayout={getItemLayout}
        // Performance optimizations
        removeClippedSubviews={true}
        maxToRenderPerBatch={5}
        updateCellsBatchingPeriod={50}
        initialNumToRender={3}
        windowSize={5}
        legacyImplementation={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  otherProductsContainer: {
    paddingHorizontal: 16,
    paddingTop: 24,
  },
  otherProductsTitle: {
    ...TypoSkin.semibold3,
    marginBottom: 12,
  },
  otherProductsList: {
    gap: ITEM_SPACING,
    paddingVertical: 12,
  },
});

export default RelatedProductsSection;
