import {FlatList, View, ActivityIndicator, Text} from 'react-native';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {useRoute} from '@react-navigation/native';
import {newsAction} from '../../redux/actions/newsAction';
import {newsEventAction} from '../../redux/actions/newEventAction';
import {useEffect, useState, useCallback} from 'react';
import {NewsItem} from '../../redux/models/news';
import {navigate, RootScreen} from '../../router/router';
import LatestNewsCard from './card/LatestNewsCard';
import {ColorThemes} from '../../assets/skin/colors';
import {CardInfoEvent} from '../event/card/CardInfoEvent';
import {NewsEvent} from '../../redux/models/newsEvent';

// Main component for displaying news or events filtered by hashtag.
const ListByHashtagScreen = () => {
  const route = useRoute();
  const {hashtag, type} = route.params as {
    hashtag: string;
    type: 'news' | 'event';
  };
  const [newsData, setNewsData] = useState<NewsItem[]>([]);
  const [eventData, setEventData] = useState<NewsEvent[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const pageSize = 1;

  useEffect(() => {
    initData();
  }, []);

  // Khởi tạo dữ liệu dựa trên loại (tin tức hoặc sự kiện) và hashtag.
  const initData = async () => {
    setLoading(true);
    try {
      if (type === 'news') {
        const res = await newsAction.fetch({
          searchRaw: `(@Hashtag:*${hashtag}*)`,
          page: 1,
          size: pageSize,
        });
        setNewsData(res.data);
        setPage(1);
        setHasMore(res.data.length < res.totalCount);
      } else if (type === 'event') {
        const res = await newsEventAction.fetch({
          searchRaw: `(@Hashtag:*${hashtag}*)`,
          page: 1,
          size: pageSize,
        });
        setEventData(res.data);
        setPage(1);
        setHasMore(res.data.length < res.totalCount);
      }
    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Xử lý tải thêm dữ liệu khi người dùng cuộn đến cuối danh sách.
  const handleLoadMore = useCallback(async () => {
    if (loadingMore || !hasMore || loading) return;

    setLoadingMore(true);
    try {
      const nextPage = page + 1;

      if (type === 'news') {
        const res = await newsAction.fetch({
          searchRaw: `(@Hashtag:*${hashtag}*)`,
          page: nextPage,
          size: pageSize,
        });

        if (res.data.length > 0) {
          setNewsData(prevData => [...prevData, ...res.data]);
          setPage(nextPage);
          const newTotalLength = newsData.length + res.data.length;
          setHasMore(newTotalLength < res.totalCount);
        } else {
          setHasMore(false);
        }
      } else if (type === 'event') {
        const res = await newsEventAction.fetch({
          searchRaw: `(@Hashtag:*${hashtag}*)`,
          page: nextPage,
          size: pageSize,
        });

        if (res.data.length > 0) {
          setEventData(prevData => [...prevData, ...res.data]);
          setPage(nextPage);
          const newTotalLength = eventData.length + res.data.length;
          setHasMore(newTotalLength < res.totalCount);
        } else {
          setHasMore(false);
        }
      }
    } catch (error) {
      console.error('Error loading more data:', error);
    } finally {
      setLoadingMore(false);
    }
  }, [
    hashtag,
    page,
    loadingMore,
    hasMore,
    loading,
    newsData.length,
    eventData.length,
    type,
  ]);

  // Hiển thị chỉ báo tải ở cuối danh sách khi đang tải thêm dữ liệu.
  const renderFooter = useCallback(() => {
    if (!loadingMore) return null;

    return (
      <View style={{paddingVertical: 20, alignItems: 'center'}}>
        <ActivityIndicator
          size="small"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }, [loadingMore]);

  // Hiển thị một mục tin tức bằng cách sử dụng thành phần LatestNewsCard.
  const renderNewsItem = useCallback(
    ({item}: {item: NewsItem}) => (
      <View style={{marginBottom: 12}}>
        <LatestNewsCard
          item={item}
          onPress={() => {
            navigate(RootScreen.DetailNews, {id: item.Id});
          }}
        />
      </View>
    ),
    [],
  );

  // Hiển thị một mục sự kiện bằng cách sử dụng thành phần CardInfoEvent.
  const renderEventItem = useCallback(
    ({item}: {item: NewsEvent}) => (
      <View style={{marginBottom: 12}}>
        <CardInfoEvent item={item} />
      </View>
    ),
    [],
  );

  // Hiển thị thành phần tiêu đề cho danh sách, hiển thị hashtag và văn bản mô tả.
  const renderListHeader = useCallback(
    () => (
      <View style={{marginBottom: 12}}>
        <Text style={{fontSize: 24, fontWeight: 'bold'}}>#{hashtag}</Text>
        <Text
          style={{
            fontSize: 16,
            color: ColorThemes.light.neutral_text_color,
          }}>
          Khám phá
        </Text>
      </View>
    ),
    [hashtag],
  );

  // Show loading for initial load
  if (loading && newsData.length === 0 && eventData.length === 0) {
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        <InforHeader title={'Khám phá'} />
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator
            size="large"
            color={ColorThemes.light.primary_main_color}
          />
        </View>
      </View>
    );
  }

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <InforHeader title={'Khám phá'} />

      {type === 'news' ? (
        <FlatList
          data={newsData}
          ListHeaderComponent={renderListHeader}
          contentContainerStyle={{
            paddingHorizontal: 8,
            paddingVertical: 8,
          }}
          renderItem={renderNewsItem}
          keyExtractor={item => item.Id}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
        />
      ) : (
        <FlatList
          data={eventData}
          ListHeaderComponent={renderListHeader}
          contentContainerStyle={{
            paddingHorizontal: 8,
            paddingVertical: 8,
          }}
          renderItem={renderEventItem}
          keyExtractor={item => item.Id}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
        />
      )}
    </View>
  );
};

export default ListByHashtagScreen;
