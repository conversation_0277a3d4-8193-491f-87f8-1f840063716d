import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { AppButton, AppSvg } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';
import iconSvg from '../../../svg/icon';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import LinearGradient from 'react-native-linear-gradient';
import { Ultis } from '../../../utils/Utils';

interface Step1IntroductionProps {
  onNext: () => void;
}

const Step1Introduction: React.FC<Step1IntroductionProps> = ({ onNext }) => {
  const customer = useSelectorCustomerState().data;
  return (
    <View style={styles.container}>
      {
        customer?.IsEnable2FA ? (
          <View style={styles.contentActive}>
           <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            style={{...styles.illustrationContainer, borderRadius: 16, alignItems: 'flex-start', paddingHorizontal: 20}}
          >
            <View style={styles.descriptionContainerActive}>
              <AppSvg SvgSrc={iconSvg.lock} size={24} />
              <View style={{
                flex: 1,
                flexDirection: 'column',
                alignItems: 'flex-start',
                paddingLeft: 10,
              }}>
                 <Text style={styles.descriptionTextActive}>
                  2MFA
                </Text>
                <Text style={{...styles.descriptionText, color: ColorThemes.light.success_main_color, fontSize: 12, fontWeight: '700'}}>
                  On Since {Ultis.formatDateTime(customer?.Date2FA, true)}
                </Text>
              </View>
              
            </View>
          </LinearGradient>
            
          </View>
        ) : <View style={styles.content}> 
        {/* Illustration */}
        <View style={styles.illustrationContainer}>
          <AppSvg SvgSrc={iconSvg.empty} size={200} />
        </View>

        {/* Description */}
        <View style={styles.descriptionContainer}>
          <Text style={styles.descriptionText}>
            Bạn chưa bật xác thực 2 lớp MFA
          </Text>
        </View>
      </View>
      }
      

      {/* Bottom Button */}
      <View style={styles.buttonContainer}>
        <AppButton
          title={customer?.IsEnable2FA ? 'Tắt xác thực 2 lớp' : 'Bật xác thực 2 lớp'}
          onPress={onNext}
          backgroundColor={ColorThemes.light.primary_main_color}
          containerStyle={styles.button}
          textStyle={styles.buttonText}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  contentActive: {
    flex: 1,
    marginTop: 20,
    
  },
  illustrationContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  illustration: {
    width: 280,
    height: 200,
  },
  descriptionContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  descriptionContainerActive: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
    
    borderRadius: 20,

  },
  descriptionText: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    lineHeight: 24,
  },
  descriptionTextActive: {
    ...TypoSkin.regular3,
    textAlign: 'center',
    lineHeight: 24,
    fontWeight: '700',
  },
  buttonContainer: {
    paddingBottom: 20,
  },
  button: {
    height: 48,
    borderRadius: 24,
    marginHorizontal: 20,
  },
  buttonText: {
    ...TypoSkin.buttonText1,
    fontWeight: '600',
    color: ColorThemes.light.white,
  },
});

export default Step1Introduction;
