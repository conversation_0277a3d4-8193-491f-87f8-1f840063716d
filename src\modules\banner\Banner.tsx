import React, {useState, useEffect} from 'react';
import BannerSection from '../Product/section/bannerSection';
import {bannerAction} from '../../redux/actions/bannerAction';
import {BannerItem} from '../../redux/models/banner';

const DefaultBanner = (query: {type: number; position: number}) => {
  const [bannerData, setBannerData] = useState<BannerItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBannerData = async () => {
      try {
        setLoading(true);
        const data = await bannerAction.find({
          page: 1,
          size: 5,
          searchRaw: `@Type: [${query.type}] @Position: [${query.position}]`,
          sortby: [{prop: 'DateCreated', direction: 'DESC'}],
        });

        setBannerData(
          data.sort((a: BannerItem, b: BannerItem) => a.Sort - b.Sort),
        );
      } catch (error) {
        setBannerData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchBannerData();
  }, []);
  return <BannerSection bannerData={bannerData} loading={loading} />;
};

export default DefaultBanner;
