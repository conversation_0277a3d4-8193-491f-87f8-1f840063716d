import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Switch,
  StyleSheet,
} from 'react-native';
import {RootScreen} from '../../router/router';
import {Title, TypeMenuPorduct} from '../../Config/Contanst';
import MenuProduct from './MenuProduct';
import ManageProductDetail from './list/ListManageProductDetail';

const ManageItemProduct = ({data}: {data: any}) => {
  const navigation = useNavigation<any>();
  const productInfo = data;
  const [menu, setMenu] = useState<string>('Còn hàng');
  return (
    <View style={styles.container}>
      <MenuProduct setMenu={setMenu} menu={menu} data={productInfo} />
      <ManageProductDetail menu={menu} dataShop={productInfo} />
      <TouchableOpacity
        style={styles.buyButton}
        onPress={() =>
          navigation.push(RootScreen.CreateNewProduct, {
            title: Title.CreateMyProduct,
          })
        }>
        <Text style={styles.createButton}>Thêm sản phẩm mới</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 10,
    position: 'relative',
    paddingBottom: 100,
  },
  buyButton: {
    backgroundColor: 'blue',
    width: '80%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
    margin: 'auto',
    marginBottom: 20,
    position: 'absolute',
    bottom: 0,
    left: '10%',
  },
  createButton: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default ManageItemProduct;
