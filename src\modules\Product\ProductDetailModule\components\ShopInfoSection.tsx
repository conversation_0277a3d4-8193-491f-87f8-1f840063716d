import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {ShopInfoSectionProps} from '../types';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';

const ShopInfoSection: React.FC<ShopInfoSectionProps> = ({
  rating = 0,
  totalProducts = 0,
  totalOrder = 0,
  onPress,
}) => {
  return (
    <View style={styles.shopInfoContainer}>
      <TouchableOpacity
        style={styles.shopInfoItem}
        onPress={() => onPress('rating')}>
        <Text style={styles.shopInfoText}>{rating?.toFixed(1) ?? '-'}</Text>
        <Text style={styles.shopInfoText}>Đánh giá</Text>
      </TouchableOpacity>
      <View style={styles.shopInfoDivider} />
      <TouchableOpacity
        style={styles.shopInfoItem}
        onPress={() => onPress('product')}>
        <Text style={styles.shopInfoText}>{totalProducts ?? '-'}</Text>
        <Text style={styles.shopInfoText}>Sản phẩm</Text>
      </TouchableOpacity>
      <View style={styles.shopInfoDivider} />
      <TouchableOpacity
        style={styles.shopInfoItem}
        onPress={() => onPress('order')}>
        <Text style={styles.shopInfoText}>{totalOrder ?? '-'}</Text>
        <Text style={styles.shopInfoText}>Đã bán</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  shopInfoContainer: {
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignContent: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
    paddingBottom: 12,
  },
  shopInfoItem: {
    gap: 2,
    alignItems: 'center',
  },
  shopInfoText: {
    ...TypoSkin.body3,
  },
  shopInfoDivider: {
    width: 1,
    height: '100%',
    backgroundColor: ColorThemes.light.neutral_main_border_color,
  },
});

export default ShopInfoSection;
