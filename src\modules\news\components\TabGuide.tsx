import {StyleSheet, Text, View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';

const TabGuide = () => {
  return (
    <View style={styles.container}>
      <Text style={{textAlign: 'center'}}>
        Chứ<PERSON> năng này đang trong giai đoạn phát triển
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});

export default TabGuide;
