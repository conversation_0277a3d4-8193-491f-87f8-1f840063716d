import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {NewsItem} from '../../../redux/models/news';
import {TypoSkin} from '../../../assets/skin/typography';

const BasicInfoNews = ({postData}: {postData: NewsItem}) => {
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
    const year = date.getFullYear();
    return `${hours}:${minutes} ngày ${day}/${month}/${year}`;
  };

  return (
    <View>
      <Text style={styles.title}>{postData.Name}</Text>

      <Text style={styles.description}>{postData.Description}</Text>

      <View style={styles.footer}>
        <Text style={styles.category}>{postData.relativeUser?.title}</Text>
        <Text style={styles.timestamp}>{formatDate(postData.DateCreated)}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    ...TypoSkin.title2,
  },
  description: {
    ...TypoSkin.title5,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  category: {
    ...TypoSkin.title5,
    color: ColorThemes.light.primary_darker_color,
  },
  timestamp: {
    ...TypoSkin.title5,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default BasicInfoNews;
