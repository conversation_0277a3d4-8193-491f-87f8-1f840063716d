import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import RenderHTML from 'react-native-render-html';
import {ProductDescriptionSectionProps} from '../types';
import {TypoSkin} from '../../../../assets/skin/typography';

const htmlStyles = {
  body: {
    color: '#313135',
    fontSize: 14,
    lineHeight: 20,
    fontFamily: 'Inter',
  },
};

const ProductDescriptionSection: React.FC<ProductDescriptionSectionProps> = ({
  content,
  windowWidth,
}) => {
  if (!content) {
    return null;
  }

  return (
    <View style={styles.descriptionContainer}>
      <Text style={styles.descriptionTitle}>Chi tiết sản phẩm</Text>
      <RenderHTML
        contentWidth={windowWidth}
        source={{html: content}}
        tagsStyles={htmlStyles}
        ignoredDomTags={['font']}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  descriptionContainer: {
    padding: 16,
  },
  descriptionTitle: {
    ...TypoSkin.semibold3,
    marginBottom: 12,
  },
});

export default ProductDescriptionSection;
