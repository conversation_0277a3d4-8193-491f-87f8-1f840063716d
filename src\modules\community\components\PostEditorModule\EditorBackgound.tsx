// PostInputComponent.js

import React from 'react';
import {
  StyleSheet,
  View,
  TextInput,
  SafeAreaView,
  StatusBar,
  ImageBackground,
  ViewStyle,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {BackgroundData} from '../../../../redux/models/PostBackground';

interface EditorBackgroundProps {
  background?: BackgroundData;
  placeholder?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  style?: ViewStyle;
}

/**
 * @description Component nhập liệu cho bài post với background tùy chỉnh theo BackgroundData.
 * @param {object} props
 * @param {BackgroundData} [props.background] - Dữ liệu background (solid, gradient, image, empty)
 * @param {string} [props.value] - G<PERSON><PERSON> trị text hiện tại
 * @param {function} [props.onChangeText] - Callback khi text thay đổi
 * @param {string} [props.placeholder] - Text placeholder
 * @param {ViewStyle} [props.style] - Custom style cho container
 */
const EditorBackground: React.FC<EditorBackgroundProps> = ({
  background,
  value,
  onChangeText,
  placeholder = 'Bạn đang nghĩ gì?',
  style,
}) => {
  // Xác định màu text - sử dụng TextColor nếu có, không thì dùng white mặc định
  const textColor = background?.TextColor || 'white';
  const placeholderColor = background?.TextColor
    ? `${background.TextColor}80`
    : '#E0E0E0'; // Add transparency

  const renderContent = () => (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" />
      <TextInput
        style={[styles.textInput, {color: textColor}]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={placeholderColor}
        multiline={true}
        textAlignVertical="center"
      />
    </SafeAreaView>
  );

  // Nếu không có background, trả về view mặc định (nền tím)
  if (!background) {
    return (
      <View style={[styles.container, styles.defaultBackground, style]}>
        {renderContent()}
      </View>
    );
  }

  // Xử lý các loại background khác nhau
  switch (background.Type) {
    case 999: // Empty background
      return (
        <View style={[styles.container, {backgroundColor: 'white'}, style]}>
          <SafeAreaView style={styles.safeArea}>
            <StatusBar barStyle="dark-content" />
            <TextInput
              style={[styles.textInput, {color: 'black'}]}
              value={value}
              onChangeText={onChangeText}
              placeholder={placeholder}
              placeholderTextColor="#A9A9A9"
              multiline={true}
              textAlignVertical="center"
            />
          </SafeAreaView>
        </View>
      );

    case 1: // Image background
      return (
        <ImageBackground
          source={{uri: background.Img}}
          style={[styles.container, style]}
          resizeMode="cover">
          <View style={styles.imageOverlay}>{renderContent()}</View>
        </ImageBackground>
      );

    case 2: // Color background (Solid or Gradient)
      const colors = background.ColorMobile
        ? background.ColorMobile.split(',')
            .map(c => c.trim())
            .filter(Boolean)
            .filter(color => {
              // Kiểm tra định dạng màu cơ bản
              return /^#[0-9A-Fa-f]{6}$|^#[0-9A-Fa-f]{3}$|^rgb\(|^rgba\(|^[a-zA-Z]+$/.test(
                color,
              );
            })
        : [];

      if (colors.length === 0) {
        return (
          <View style={[styles.container, styles.defaultBackground, style]}>
            {renderContent()}
          </View>
        );
      }

      if (colors.length < 2) {
        // Solid color
        const singleColor = colors[0];
        if (!singleColor) {
          return (
            <View style={[styles.container, styles.defaultBackground, style]}>
              {renderContent()}
            </View>
          );
        }
        return (
          <View
            style={[styles.container, {backgroundColor: singleColor}, style]}>
            {renderContent()}
          </View>
        );
      } else {
        // Gradient
        const validColors = colors.slice(0, 10); // Giới hạn số màu để tránh performance issues
        return (
          <LinearGradient
            colors={validColors}
            start={{x: 0, y: 0}}
            end={{x: 0, y: 1}}
            style={[styles.container, style]}>
            {renderContent()}
          </LinearGradient>
        );
      }

    default:
      // Fallback - trả về view mặc định
      return (
        <View style={[styles.container, styles.defaultBackground, style]}>
          {renderContent()}
        </View>
      );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  defaultBackground: {
    backgroundColor: '#6A0DAD', // Màu mặc định khi không có background
  },
  safeArea: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textInput: {
    width: '90%', // Chiều rộng 90% để không quá sát 2 bên
    fontSize: 28,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  imageOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Overlay tối nhẹ để text dễ đọc trên background image
    width: '100%',
  },
});

export default EditorBackground;
