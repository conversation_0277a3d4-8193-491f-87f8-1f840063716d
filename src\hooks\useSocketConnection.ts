import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store/store';
import AuthSocketService from '../services/AuthSocketService';
import { useSelectorCustomerState } from '../redux/hook/customerHook';

/**
 * Hook để quản lý trạng thái kết nối socket
 * Tự động kết nối/ngắt kết nối dựa trên trạng thái đăng nhập
 */
export const useSocketConnection = () => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  
  const customer = useSelectorCustomerState().data;
  const chatState = useSelector((state: RootState) => state.chat);
  
  // Kiểm tra trạng thái kết nối
  const isConnected = chatState.isConnected && AuthSocketService.isConnected();

  // <PERSON><PERSON>m kết nối thủ công
  const connect = async () => {
    if (!customer?.Id) {
      setConnectionError('User not logged in');
      return false;
    }

    setIsConnecting(true);
    setConnectionError(null);

    try {
      const success = await AuthSocketService.initializeSocketConnection(customer?.Id ?? '',customer?.Name ?? '');
      if (!success) {
        setConnectionError('Failed to connect to socket');
      }
      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setConnectionError(errorMessage);
      return false;
    } finally {
      setIsConnecting(false);
    }
  };

  // Hàm ngắt kết nối thủ công
  const disconnect = () => {
    AuthSocketService.disconnectSocket();
    setConnectionError(null);
  };

  // Hàm retry kết nối
  const retry = async () => {
    setConnectionError(null);
    return await connect();
  };

  // Auto-disconnect khi user đăng xuất (không auto-connect để tránh duplicate)
  useEffect(() => {
    const handleUserChange = async () => {
      if (!customer?.Id) {
        // User đăng xuất, ngắt kết nối
        if (isConnected) {
          console.log('🔌 Disconnecting socket for logged out user...');
          disconnect();
        }
      }
    };

    handleUserChange();
  }, [customer?.Id]);

  // Cleanup khi component unmount
  useEffect(() => {
    return () => {
      // Không ngắt kết nối khi component unmount vì socket cần duy trì
      // chỉ ngắt khi user thực sự đăng xuất
    };
  }, []);

  return {
    isConnected,
    isConnecting,
    connectionError,
    connect,
    disconnect,
    retry,
  };
};

/**
 * Hook đơn giản chỉ để kiểm tra trạng thái kết nối
 */
export const useSocketStatus = () => {
  const chatState = useSelector((state: RootState) => state.chat);
  return {
    isConnected: chatState.isConnected && AuthSocketService.isConnected(),
    connectionStatus: chatState.isConnected ? 'connected' : 'disconnected',
  };
};
