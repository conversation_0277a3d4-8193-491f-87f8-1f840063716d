import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  StatusBar,
  Platform,
} from 'react-native';
// import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { ColorThemes } from '../../assets/skin/colors';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../Config/ConfigAPI';
import { Winicon } from 'wini-mobile-components';

interface FloatingIncomingCallProps {
  visible: boolean;
  callerName: string;
  callerAvatar?: string;
  onAccept: () => void;
  onReject: () => void;
  onMinimize?: () => void;
}

const FloatingIncomingCall: React.FC<FloatingIncomingCallProps> = ({
  visible,
  callerName,
  callerAvatar,
  onAccept,
  onReject,
  onMinimize,
}) => {
  const slideAnim = useRef(new Animated.Value(-200)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    let pulseAnimation: Animated.CompositeAnimation;

    if (visible) {
      // Slide down animation
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      // Pulse animation for avatar
      pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
    } else {
      // Slide up animation
      Animated.timing(slideAnim, {
        toValue: -200,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }

    return () => {
      if (pulseAnimation) pulseAnimation.stop();
    };
  }, [visible]);

  // Simplified dismiss handler
  const handleDismiss = () => {
    if (onMinimize) {
      onMinimize();
    }
  };

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }]
        }
      ]}
    >
      <StatusBar barStyle="dark-content" />

      <TouchableOpacity
        style={styles.card}
        activeOpacity={0.95}
        onLongPress={handleDismiss}
      >
        <Animated.View style={[styles.avatarContainer, { transform: [{ scale: pulseAnim }] }]}>
          {callerAvatar ? (
            <FastImage
              source={{ uri: ConfigAPI.urlImg + callerAvatar }}
              style={styles.avatar}
            />
          ) : (
            <View style={styles.defaultAvatar}>
              <Text style={styles.avatarText}>
                {callerName?.charAt(0).toUpperCase() || '?'}
              </Text>
            </View>
          )}
        </Animated.View>

        <View style={styles.infoContainer}>
          <Text style={styles.name}>{callerName || 'Người dùng'}</Text>
          <Text style={styles.subtext}>Cuộc gọi đến • Nhấn giữ để ẩn</Text>
        </View>

        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.circleButton, styles.reject]}
            onPress={onReject}
            activeOpacity={0.8}
          >
            <Winicon src="fill/user interface/close" size={18} color="#fff" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.circleButton, styles.accept]}
            onPress={onAccept}
            activeOpacity={0.8}
          >
            <Winicon src="fill/user interface/phone-call" size={18} color="#fff" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : (StatusBar.currentHeight || 0) + 10,
    left: 8,
    right: 8,
    zIndex: 10000,
    backgroundColor: 'transparent',
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOpacity: 0.15,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    elevation: 8,
    minHeight: 70,
  },
  avatarContainer: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    overflow: 'hidden',
    marginRight: 12,
  },
  avatar: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    width: 45,
    height: 45,
    borderRadius: 22.5,
  },
  avatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 4,
  },
  name: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  subtext: {
    fontSize: 12,
    color: '#666',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  circleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 4,
  },
  reject: {
    backgroundColor: '#f44336',
  },
  accept: {
    backgroundColor: '#4CAF50',
  },
});

export default FloatingIncomingCall;
