import {DataController} from '../../base/baseController';
import {
  StatusOrder,
  StorageContanst,
  TypeMenuPorduct,
} from '../../Config/Contanst';
import {getImage} from '../../redux/actions/rootAction';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';

export class ProductDA {
  private ProductController: DataController;
  private orderDetailController: DataController;
  private ratingController: DataController;

  constructor(
    ProductController = new DataController('Product'),
    orderDetailController = new DataController('OrderDetail'),
    ratingController = new DataController('Rating'),
  ) {
    this.ProductController = ProductController;
    this.orderDetailController = orderDetailController;
    this.ratingController = ratingController;
  }

  async getProductPopular(pageSize: number) {
    const responseProduct = await this.orderDetailController.group({
      searchRaw: '*',
      reducers: 'LOAD * GROUPBY 1 @ProductId REDUCE COUNT 0 AS CountProduct',
    });

    if (responseProduct.code === 200) {
      var lstProduct = responseProduct.data;
      //sắp xếp lại khóa học sau khi đã count trong đơn hàng để lấy khóa đc mua nhiều nhất. và lấy top 10
      if (lstProduct.length > 0) {
        if (pageSize > 0) {
          lstProduct = [...lstProduct]
            .sort(
              (a, b) =>
                parseInt(a.CountProduct, 10) - parseInt(b.CountProduct, 10),
            )
            .slice(0, pageSize);
        } else {
          lstProduct = [...lstProduct].sort(
            (a, b) =>
              parseInt(a.CountProduct, 10) - parseInt(b.CountProduct, 10),
          );
        }

        const respone = await this.ProductController.getListSimple({
          page: 1,
          size: 50,
          query: `@Id: {${lstProduct
            .map((item: any) => item.ProductId)
            .join(' | ')}}`,
          returns: [
            'Id',
            'Name',
            'Price',
            'Img',
            'CategoryId',
            'ShopId',
            'Discount',
          ],
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (respone.code === 200) {
          return respone;
        }
      } else {
        const respone = await this.ProductController.getListSimple({
          page: 1,
          size: 20,
          query: '@IsHot: {true}',
          returns: [
            'Id',
            'Name',
            'Price',
            'Img',
            'CategoryId',
            'ShopId',
            'Discount',
          ],
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (respone.code === 200) {
          return respone;
        }
      }
    }
    return null;
  }
  //lấy danh sach sản phẩm hot
  async getProductHot(page?: number, size?: number) {
    const respone = await this.ProductController.getPatternList({
      page: page,
      size: size,
      query: `@IsHot: {true} @Status: [${TypeMenuPorduct.InStock.id}]`,
      returns: [
        'Id',
        'Name',
        'Price',
        'Img',
        'Discount',
        'ShopId',
        'Sold',
        'CategoryId',
      ],
      pattern: {
        ShopId: ['Id', 'Name', 'Avatar', 'Address', 'Mobile', 'Email'],
      },
    });

    if (respone.code === 200) {
      // lấy rating trong ratingController

      let lstId = respone.data.map((item: any) => item.Id);
      lstId = [...new Set(lstId)];
      const ratingResult = await this.ratingController.getListSimple({
        query: `@ProductId: {${lstId.join(' | ')}}`,
      });
      if (ratingResult.code === 200) {
        respone.data.map((item: any) => {
          const rating = ratingResult.data.filter(
            (a: any) => a.ProductId === item.Id,
          );
          item.rating =
            rating.length > 0
              ? rating.reduce((acc: any, cur: any) => acc + cur.Value, 0) /
                rating.length
              : 0;
          return item;
        });
      }

      return respone;
    }
    return null;
  }
  //lấy danh sách sản phẩm bán chạy
  async getProductBestSeller(page?: number, size?: number) {
    const respone = await this.ProductController.getListSimple({
      page: 1,
      size: 50,
      query: `@Sold: [0 +inf] @Status: [${TypeMenuPorduct.InStock.id}]`,
      returns: [
        'Id',
        'Name',
        'Price',
        'Img',
        'Discount',
        'ShopId',
        'Sold',
        'CategoryId',
      ],
      sortby: {BY: 'Sold', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      let lstId = respone.data.map((item: any) => item.Id);
      lstId = [...new Set(lstId)];
      const ratingResult = await this.ratingController.getListSimple({
        query: `@ProductId: {${lstId.join(' | ')}}`,
      });
      if (ratingResult.code === 200) {
        respone.data.map((item: any) => {
          const rating = ratingResult.data.filter(
            (a: any) => a.ProductId === item.Id,
          );
          item.rating =
            rating.length > 0
              ? rating.reduce((acc: any, cur: any) => acc + cur.Value, 0) /
                rating.length
              : 0;
          return item;
        });
      }
      const data = getImage({items: respone.data});
      return data;
    }
    return [];
  }

  async getAllList(
    page: number,
    size: number,
    query: string,
    returns?: Array<string>,
  ) {
    const respone = await this.ProductController.getListSimple({
      page: page,
      size: size,
      query: query ?? '*',
      returns: returns,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getAllListbyCategory(
    page?: any,
    size?: any,
    cateId?: string,
    isHot?: boolean,
  ) {
    const respone = await this.ProductController.getListSimple({
      page: page,
      size: size,
      query: isHot
        ? `@IsHot:{true} @CategoryId:{${cateId}}`
        : `@CategoryId:{${cateId}}`,
      // returns: ['Id', 'Name', 'Price', 'Img', 'CustomerId'],
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });

    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getAllListbyCustomerId(page: number, size: number, customerId: string) {
    const respone = await this.ProductController.getListSimple({
      page: page,
      size: size,
      query: `@CustomerId:{${customerId}}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getProductDetail(ProductId: string) {
    const respone = await this.ProductController.getPatternList({
      query: `@Id:{${ProductId}}`,
      pattern: {
        ShopId: ['Id', 'Name', 'Address', 'Mobile', 'Email', 'CustomerId'],
        CategoryId: ['Id', 'Name', 'ParentId'],
        BrandId: ['Id', 'Name'],
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
}
