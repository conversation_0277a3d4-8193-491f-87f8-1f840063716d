import React, {useEffect, useState} from 'react';
import {FlatList} from 'react-native-gesture-handler';
import ListItemCard from '../card/CartProductCreate';
import {ListItemProps} from '../../dto/dto';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';

const ListItem = (props: ListItemProps) => {
  const {
    setSelecChildID,
    setSelecChildName,
    selectItemChild,
    setSelectItemChild,
    dataCategory: propDataCategory,
    backSelect,
    setBackSelect,
  } = props;
  const [isSelected, setIsSelected] = useState('');
  const [data, setData] = useState<any[]>();
  const {data: dataCategory} = useSelector(
    (state: RootState) => state.category,
  );

  // Use prop dataCategory if provided, otherwise use from Redux store
  const categoryData = propDataCategory || dataCategory;

  useEffect(() => {
    console.log('check-dataCategory', dataCategory);
  }, [dataCategory]);

  const handleSelect = (item: any) => {
    console.log('check-item', item);
    setSelecChildID(item?.Id);
    setSelecChildName(item?.Name);
    if (item?.Id !== isSelected) {
      setIsSelected(item.Id);
    } else {
      setIsSelected('');
    }
  };

  useEffect(() => {
    console.log('check-data', data);
  }, [data]);

  useEffect(() => {
    if (selectItemChild) {
      console.log('check-selectItemChild', selectItemChild);
      if (selectItemChild?.Children && selectItemChild?.Children?.length > 0) {
        setData(selectItemChild?.Children);
      } else {
        setData(categoryData);
      }
    }
  }, [selectItemChild]);

  useEffect(() => {
    if (backSelect) {
      setData(categoryData);
      setBackSelect && setBackSelect(false);
    }
  }, [backSelect]);

  return (
    <FlatList
      data={data ? data : categoryData}
      style={{flex: 1, marginBottom: 50}}
      keyExtractor={(item, i) => `${i} ${item.Id}`}
      renderItem={({item, index}) =>
        ListItemCard(
          {item, index},
          isSelected,
          handleSelect,
          selectItemChild,
          setSelectItemChild,
        )
      }
    />
  );
};

export default ListItem;
