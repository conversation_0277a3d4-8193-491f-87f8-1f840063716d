import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import SearchBar from '../../components/shop/Search';
import { TypoSkin } from '../../assets/skin/typography';
import { ColorThemes } from '../../assets/skin/colors';
import { Ultis } from '../../utils/Utils';

// Mock data để test
const mockOrders = [
  {
    Id: '1',
    Code: 'ORD001',
    Value: 150000,
    DateCreated: new Date('2024-01-15').getTime(),
    Shop: { Name: 'Cửa hàng ABC' }
  },
  {
    Id: '2', 
    Code: 'ORD002',
    Value: 250000,
    DateCreated: new Date('2024-02-20').getTime(),
    Shop: { Name: 'Shop XYZ' }
  },
  {
    Id: '3',
    Code: 'ORD003', 
    Value: 75000,
    DateCreated: new Date('2024-03-10').getTime(),
    Shop: { Name: 'C<PERSON><PERSON> hàng DEF' }
  }
];

const TestOrderSearch = () => {
  const [dataSearch, setDataSearch] = useState<string>('');
  const [filteredData, setFilteredData] = useState(mockOrders);

  // Hàm tìm kiếm giống như trong OrderCustomerDetail
  const filterData = (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setFilteredData(mockOrders);
      return;
    }

    const term = searchTerm.toLowerCase().trim();
    
    const filtered = mockOrders.filter(item => {
      // Tìm kiếm theo mã đơn hàng
      const codeMatch = item.Code?.toLowerCase().includes(term);
      
      // Tìm kiếm theo tên shop
      const shopNameMatch = item.Shop?.Name?.toLowerCase().includes(term);
      
      // Tìm kiếm theo số tiền
      let valueMatch = false;
      if (item.Value) {
        const valueStr = item.Value.toString();
        const formattedValue = Ultis.money(item.Value);
        
        valueMatch = valueStr.includes(term) || 
                    formattedValue.toLowerCase().includes(term) ||
                    formattedValue.replace(/\./g, '').includes(term.replace(/\./g, '')) ||
                    formattedValue.replace(/,/g, '').includes(term.replace(/,/g, ''));
      }
      
      // Tìm kiếm theo thời gian
      let dateMatch = false;
      if (item.DateCreated) {
        const formattedDate = Ultis.formatDateTime(item.DateCreated, true);
        dateMatch = formattedDate.toLowerCase().includes(term);
        
        const dateOnly = new Date(item.DateCreated).toLocaleDateString('vi-VN');
        dateMatch = dateMatch || dateOnly.includes(term);
      }
      
      return codeMatch || shopNameMatch || valueMatch || dateMatch;
    });

    setFilteredData(filtered);
  };

  const handleSearch = (text: string) => {
    setDataSearch(text);
    filterData(text);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Test Tìm Kiếm Đơn Hàng</Text>
      
      <SearchBar 
        setDataSearch={handleSearch}
        placeholder="Tìm theo mã đơn, tên shop, số tiền hoặc ngày (dd/mm/yyyy)"
      />
      
      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          Tìm thấy: {filteredData.length} / {mockOrders.length} đơn hàng
        </Text>
        {dataSearch.trim() && (
          <Text style={styles.searchTerm}>
            Từ khóa: "{dataSearch}"
          </Text>
        )}
      </View>

      <ScrollView style={styles.resultContainer}>
        {filteredData.map((item, index) => (
          <View key={item.Id} style={styles.orderItem}>
            <Text style={styles.orderCode}>Mã: {item.Code}</Text>
            <Text style={styles.orderShop}>Shop: {item.Shop.Name}</Text>
            <Text style={styles.orderValue}>
              Giá trị: {Ultis.money(item.Value)} đ
            </Text>
            <Text style={styles.orderDate}>
              Ngày: {Ultis.formatDateTime(item.DateCreated, true)}
            </Text>
            <Text style={styles.orderDateOnly}>
              Ngày (dd/mm/yyyy): {new Date(item.DateCreated).toLocaleDateString('vi-VN')}
            </Text>
          </View>
        ))}
        
        {filteredData.length === 0 && dataSearch.trim() && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Không tìm thấy đơn hàng nào</Text>
          </View>
        )}
      </ScrollView>

      <View style={styles.instructionContainer}>
        <Text style={styles.instructionTitle}>Hướng dẫn test:</Text>
        <Text style={styles.instructionText}>• Tìm theo mã: "ORD001"</Text>
        <Text style={styles.instructionText}>• Tìm theo shop: "ABC" hoặc "XYZ"</Text>
        <Text style={styles.instructionText}>• Tìm theo số tiền: "150000" hoặc "150.000"</Text>
        <Text style={styles.instructionText}>• Tìm theo ngày: "15/01/2024" hoặc "15/1/2024"</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
  },
  title: {
    ...TypoSkin.heading5,
    textAlign: 'center',
    marginBottom: 20,
    color: ColorThemes.light.primary_main_color,
  },
  infoContainer: {
    marginVertical: 10,
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  infoText: {
    ...TypoSkin.regular2,
    fontWeight: '600',
  },
  searchTerm: {
    ...TypoSkin.regular3,
    color: '#666',
    marginTop: 4,
  },
  resultContainer: {
    flex: 1,
    marginTop: 10,
  },
  orderItem: {
    backgroundColor: '#f9f9f9',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: ColorThemes.light.primary_main_color,
  },
  orderCode: {
    ...TypoSkin.regular2,
    fontWeight: '600',
    color: ColorThemes.light.primary_main_color,
  },
  orderShop: {
    ...TypoSkin.regular3,
    marginTop: 2,
  },
  orderValue: {
    ...TypoSkin.regular3,
    marginTop: 2,
    color: '#DA251D',
    fontWeight: '600',
  },
  orderDate: {
    ...TypoSkin.regular3,
    marginTop: 2,
    color: '#666',
  },
  orderDateOnly: {
    ...TypoSkin.regular3,
    marginTop: 2,
    color: '#999',
    fontSize: 11,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    ...TypoSkin.regular2,
    color: '#999',
  },
  instructionContainer: {
    marginTop: 20,
    padding: 12,
    backgroundColor: '#e3f2fd',
    borderRadius: 8,
  },
  instructionTitle: {
    ...TypoSkin.regular2,
    fontWeight: '600',
    marginBottom: 8,
  },
  instructionText: {
    ...TypoSkin.regular3,
    marginBottom: 4,
  },
});

export default TestOrderSearch;
