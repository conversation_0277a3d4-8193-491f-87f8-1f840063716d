import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  StatusBar,
  Platform,
} from 'react-native';
import { ColorThemes } from '../../assets/skin/colors';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../Config/ConfigAPI';
import { Winicon } from 'wini-mobile-components';

interface IncomingCallNotificationProps {
  visible: boolean;
  callerName: string;
  callerAvatar?: string;
  onAccept: () => void;
  onReject: () => void;
}

const IncomingCallNotification: React.FC<IncomingCallNotificationProps> = ({
  visible,
  callerName,
  callerAvatar,
  onAccept,
  onReject,
}) => {
  const slideAnim = useRef(new Animated.Value(-200)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const overlayOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    let pulseAnimation: Animated.CompositeAnimation;

    if (visible) {
      // Fade in overlay
      Animated.timing(overlayOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Slide down notification
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      // Pulse animation for avatar
      pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
    } else {
      // Fade out overlay
      Animated.timing(overlayOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Slide up notification
      Animated.timing(slideAnim, {
        toValue: -200,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }

    return () => {
      if (pulseAnimation) pulseAnimation.stop();
    };
  }, [visible]);

  if (!visible) return null;

  return (
    <Animated.View style={[styles.overlay, { opacity: overlayOpacity }]}>
      <StatusBar barStyle="light-content" backgroundColor="rgba(0,0,0,0.5)" />

      {/* Touch area để dismiss - chỉ phần trên notification */}
      <TouchableOpacity
        style={styles.dismissArea}
        activeOpacity={1}
        onPress={() => {
          // Có thể thêm logic để minimize notification thay vì reject
          console.log('📞 User tapped outside notification');
        }}
      />

      <Animated.View style={[styles.container, { transform: [{ translateY: slideAnim }] }]}>
        <View style={styles.card}>
          <Animated.View style={[styles.avatarContainer, { transform: [{ scale: pulseAnim }] }]}>
            {callerAvatar ? (
              <FastImage
                source={{ uri: ConfigAPI.urlImg + callerAvatar }}
                style={styles.avatar}
              />
            ) : (
              <View style={styles.defaultAvatar}>
                <Text style={styles.avatarText}>
                  {callerName?.charAt(0).toUpperCase() || '?'}
                </Text>
              </View>
            )}
          </Animated.View>

          <View style={styles.infoContainer}>
            <Text style={styles.name}>{callerName || 'Người dùng'}</Text>
            <Text style={styles.subtext}>Cuộc gọi đến</Text>
          </View>

          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.circleButton, styles.reject]}
              onPress={onReject}
              activeOpacity={0.8}
            >
              <Winicon src="outline/layout/xmark" size={20} color="#fff" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.circleButton, styles.accept]}
              onPress={onAccept}
              activeOpacity={0.8}
            >
              <Winicon src="fill/user interface/phone-call" size={20} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>

      {/* Bottom area - cho phép tương tác với app bên dưới */}
      <View style={styles.bottomArea} pointerEvents="none" />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 200, // Chỉ cover phần top thay vì toàn màn hình
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Nhẹ hơn
    zIndex: 10000,
    justifyContent: 'flex-start',
  },
  container: {
    marginTop: Platform.OS === 'ios' ? 50 : (StatusBar.currentHeight || 0) + 10,
    marginHorizontal: 8,
    backgroundColor: 'transparent',
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    elevation: 8,
    minHeight: 80,
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  infoContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 4,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  subtext: {
    fontSize: 14,
    color: '#666',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  circleButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 4,
  },
  reject: {
    backgroundColor: '#f44336',
  },
  accept: {
    backgroundColor: '#4CAF50',
  },
  dismissArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: Platform.OS === 'ios' ? 50 : (StatusBar.currentHeight || 0) + 10,
    backgroundColor: 'transparent',
  },
  bottomArea: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '70%', // 70% bottom của màn hình cho phép tương tác
    backgroundColor: 'transparent',
  },
});

export default IncomingCallNotification;
