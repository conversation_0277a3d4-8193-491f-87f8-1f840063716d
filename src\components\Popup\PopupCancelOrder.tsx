import React, {useState} from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';

interface PopupCancelOrderProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (reason: string) => void;
  loading?: boolean;
}

const PopupCancelOrder: React.FC<PopupCancelOrderProps> = ({
  visible,
  onClose,
  onSubmit,
  loading,
}) => {
  const [reason, setReason] = useState('');

  // Reset reason when popup is closed
  React.useEffect(() => {
    if (!visible) {
      setReason('');
    }
  }, [visible]);

  const handleSubmit = () => {
    if (reason.trim()) {
      onSubmit(reason);
    } else {
      console.log('PopupCancelOrder: reason is empty, not submitting');
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.dialogContainer}>
          <Text style={styles.title}>Lý do từ chối đơn hàng</Text>
          <TextInput
            style={styles.input}
            placeholder="Nhập lý do..."
            placeholderTextColor="#999"
            multiline
            value={reason}
            onChangeText={setReason}
          />
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}>
              <Text style={styles.buttonText}>Huỷ</Text>
            </TouchableOpacity>
            <TouchableOpacity
              disabled={!reason.trim() || loading}
              style={[
                styles.button,
                styles.confirmButton,
                (!reason.trim() || loading) && styles.disabledButton,
              ]}
              onPress={handleSubmit}>
              {loading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={[styles.buttonText, styles.confirmButtonText]}>
                  Xác nhận
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogContainer: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    elevation: 5,
  },
  title: {
    ...TypoSkin.heading5,
    textAlign: 'center',
    marginBottom: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
    textAlignVertical: 'top',
    marginBottom: 20,
    ...TypoSkin.body2,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
  },
  confirmButton: {
    backgroundColor: ColorThemes.light.error_main_color,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  buttonText: {
    ...TypoSkin.buttonText2,
    color: '#111',
  },
  confirmButtonText: {
    color: 'white',
  },
});

export default PopupCancelOrder;
