interface NotificationItem {
  Id: string;
  Name: string;
  DateCreated: number;
  Sort: number;
  Content: string;
  Status: number;
  LinkWeb: string;
  LinkApp: string;
  CustomerId: string;
  Type: number;
}

interface NotificationConfigItem {
  Id: string;
  Name: string;
  DateCreated: number;
  Sort: number;
  Content: string;
  Status: number; // 0: unread, 1: read
  CustomerId: string;
}

export enum NotificationType {
  GENERAL = 1,
  AFFILIATE_IN = 2,
  AFFILIATE_OUT = 3,
}

export enum NotificationStatus {
  UNREAD = 0,
  READ = 1,
}

const dataCreate = [
  // Hôm nay
  {
    Id: '1',
    Name: 'Giao dịch đổi quà',
    Sort: 1,
    Content: 'Giao dịch đổi quà của bạn vừa được phê duyệt, bạn bị trừ (-900$)',
    Status: 0, // unread
    Type: '3',
    CustomerId: '1',
    LinkWeb: '1',
    LinkApp: '1',
  },
  {
    Id: '2',
    Name: 'Nhận hoa hồng',
    Sort: 2,
    Content:
      'Bạn vừa nhận được 200$ hoa hồng từ giao dịch mua hàng của "Nguyễn Thanh Tùng"',
    Status: 1, // read
    Type: '2',
    LinkWeb: '1',
    LinkApp: '1',
  },
  // Ngày hôm trước
  {
    Id: '3',
    Name: 'Nhận hoa hồng',
    Sort: 3,
    Content:
      'Bạn vừa nhận được 200$ hoa hồng từ giao dịch mua hàng của "Nguyễn Thanh Tùng"',
    Status: 0, // unread
    Type: '2',
    LinkWeb: '1',
    LinkApp: '1',
  },
  {
    Id: '4',
    Name: 'Giao dịch đổi quà',
    Sort: 4,
    Content: 'Giao dịch đổi quà của bạn vừa được phê duyệt, bạn bị trừ (-900$)',
    Status: 1, // read
    Type: '3',
    LinkWeb: '1',
    LinkApp: '1',
  },
  {
    Id: '5',
    Name: 'Bảo trì hệ thống',
    Sort: 5,
    Content:
      'Hệ thống sẽ cập nhật trong 5 phút tới, bạn vui lòng hoàn thành các công việc tồn đọng',
    Status: 0, // read
    Type: '1',
    LinkWeb: '1',
    LinkApp: '1',
  },
];

export type {NotificationItem, NotificationConfigItem};
