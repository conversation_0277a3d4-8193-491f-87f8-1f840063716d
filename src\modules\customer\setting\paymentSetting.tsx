import {useNavigation, useRoute} from '@react-navigation/native';
import {useState, useEffect} from 'react';
import {
  Dimensions,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {ColorThemes} from '../../../assets/skin/colors';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import {DataController} from '../../../base/baseController';
import {AppButton, FLoading, Winicon} from 'wini-mobile-components';
import WScreenFooter from '../../../Screen/Layout/footer';
import {RootScreen} from '../../../router/router';
import {SafeAreaView} from 'react-native-safe-area-context';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {TypoSkin} from '../../../assets/skin/typography';
import {ScrollView} from 'react-native-gesture-handler';
import EmptyPage from '../../../Screen/emptyPage';

export default function PaymentSetting() {
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const dispatch = useDispatch<any>();
  const [data, setData] = useState<Array<any>>([]);
  const [refresh, setrefresh] = useState(false);
  const now = new Date();
  const route = useRoute<any>();

  return (
    <SafeAreaView
      edges={['bottom']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <InforHeader title="QL Thanh toán" onBack={() => navigation.goBack()} />
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refresh}
            onRefresh={async () => {
              setrefresh(true);
              await dispatch(CustomerActions.getAddresses(user.Id));
              setrefresh(false);
            }}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        style={{flex: 1}}
        showsVerticalScrollIndicator={false}></ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  orderInfoRow: {
    flexDirection: 'row',
    padding: 16,
    borderBottomColor: '#EEEEEE',
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  orderInfoIcon: {
    width: 24,
    marginRight: 12,
  },
  orderInfoContent: {
    flex: 1,
    gap: 4,
  },
  orderInfoLabel: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  orderInfoValue: {
    ...TypoSkin.body3,
    color: '#757575',
  },
});
