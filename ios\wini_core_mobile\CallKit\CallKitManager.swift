import UIKit
import CallKit
import AVFoundation
import react_native_webrtc

class CallKitManager: NSObject {
    static let shared = CallKitManager()
    private let provider: CXProvider
    
    private override init() {
        let providerConfiguration = CXProviderConfiguration(localizedName: "Wini Core")
        providerConfiguration.supportsVideo = true
        providerConfiguration.maximumCallsPerCallGroup = 1
        providerConfiguration.supportedHandleTypes = [.phoneNumber]
        
        provider = CXProvider(configuration: providerConfiguration)
        
        super.init()
        
        provider.setDelegate(self, queue: nil)
    }
    
    // Add your CallKit implementation methods here
}

// MARK: - CXProviderDelegate methods
extension CallKitManager: CXProviderDelegate {
    
    func provider(_ provider: CXProvider, didActivate audioSession: AVAudioSession) {
        RTCAudioSession.sharedInstance().audioSessionDidActivate(AVAudioSession.sharedInstance())
    }
    
    func provider(_ provider: CXProvider, didDeactivate audioSession: AVAudioSession) {
        RTCAudioSession.sharedInstance().audioSessionDidDeactivate(AVAudioSession.sharedInstance())
    }
    
    // Implement other required CXProviderDelegate methods
    func providerDidReset(_ provider: CXProvider) {
        // Handle provider reset
    }
}
