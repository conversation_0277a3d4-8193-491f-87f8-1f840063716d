# Chức năng Chat 1-1 và Chat Nhóm

## Tổng quan

Chức năng chat được xây dựng với các tính năng:
- Chat 1-1 và chat nhóm
- G<PERSON>i tin nhắn text, emoji, ảnh, file
- Realtime messaging với Socket.IO
- Hiển thị trạng thái đã đọc/chưa đọc
- Tải thêm tin nhắn cũ (pagination)
- Giao diện sử dụng react-native-gifted-chat

## Cấu trúc thư mục

```
src/modules/chat/
├── components/
│   ├── ChatBadge.tsx          # Badge hiển thị số tin nhắn chưa đọc
│   └── EmojiPicker.tsx        # Component chọn emoji
├── screens/
│   ├── ChatListScreen.tsx     # Danh sách cuộc trò chuyện
│   ├── ChatRoomScreen.tsx     # Màn hình chat
│   └── CreateGroupScreen.tsx  # Tạo nhóm chat
├── services/
│   ├── ChatAPI.ts            # API calls cho chat
│   └── SocketService.ts      # Socket.IO service
└── types/
    └── ChatTypes.ts          # Type definitions
```

## Dependencies đã cài đặt

- `react-native-gifted-chat`: Thư viện chat chính
- `socket.io-client`: Realtime messaging
- `react-native-image-crop-picker`: Chọn và crop ảnh
- `react-native-document-picker`: Chọn file

## Cấu hình Android

### Permissions đã thêm vào AndroidManifest.xml:
```xml
<!-- Permissions for document picker -->
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
```

### File Provider đã cấu hình:
- `android/app/src/main/res/xml/file_paths.xml`
- Provider trong AndroidManifest.xml cho sharing files

### Auto-linking:
- Tất cả thư viện sử dụng auto-linking
- Không cần cấu hình thêm trong MainApplication.kt

## Cách sử dụng

### 1. Truy cập Chat Demo

Từ màn hình Home, nhấn button "Chat Demo" để:
- Tải dữ liệu demo
- Test các chức năng chat
- Điều hướng đến các màn hình chat

### 2. Danh sách Chat

- Hiển thị tất cả cuộc trò chuyện
- Thông tin: avatar, tên, tin nhắn cuối, thời gian
- Badge hiển thị số tin nhắn chưa đọc
- Pull-to-refresh để cập nhật

### 3. Màn hình Chat

- Giao diện chat với react-native-gifted-chat
- Gửi tin nhắn text
- Chọn và gửi emoji
- Chụp ảnh hoặc chọn từ thư viện
- Chọn và gửi file
- Tải thêm tin nhắn cũ khi scroll lên

### 4. Tạo nhóm chat

- Tìm kiếm và chọn thành viên
- Nhập tên nhóm
- Tạo nhóm và bắt đầu chat

## Navigation

Chat được tích hợp vào bottom navigation:
- Tab "chat" thay thế tab "call" cũ
- Badge hiển thị tổng số tin nhắn chưa đọc
- Yêu cầu đăng nhập để sử dụng

## Redux State

```typescript
interface ChatState {
  rooms: ChatRoom[];           // Danh sách phòng chat
  currentRoom: ChatRoom | null; // Phòng chat hiện tại
  messages: { [roomId: string]: ChatMessage[] }; // Tin nhắn theo phòng
  loading: boolean;            // Trạng thái loading
  error: string | null;        // Lỗi nếu có
  isConnected: boolean;        // Trạng thái kết nối socket
}
```

## Socket Events

### Client gửi:
- `join_room`: Tham gia phòng chat
- `leave_room`: Rời khỏi phòng chat
- `send_message`: Gửi tin nhắn
- `typing`: Thông báo đang gõ

### Client nhận:
- `new_message`: Tin nhắn mới
- `message_update`: Cập nhật tin nhắn
- `room_update`: Cập nhật phòng chat
- `user_typing`: Người dùng đang gõ

## API Endpoints

### Mock Data (Development)
- Sử dụng dữ liệu demo trong `src/mock/chatData.ts`
- Bật/tắt bằng flag `useMockData` trong `ChatAPI.ts`

### Production API
```
GET /api/chat/rooms          # Lấy danh sách phòng chat
GET /api/chat/messages       # Lấy tin nhắn
POST /api/chat/create-room   # Tạo phòng chat
POST /api/chat/send-message  # Gửi tin nhắn
POST /api/chat/upload-file   # Upload file
```

## Tính năng đã hoàn thành

✅ **Giao diện cơ bản**
- ChatListScreen với danh sách cuộc trò chuyện
- ChatRoomScreen với react-native-gifted-chat
- CreateGroupScreen để tạo nhóm

✅ **Chức năng cơ bản**
- Gửi/nhận tin nhắn text
- Hiển thị avatar, tên, thời gian
- Badge số tin nhắn chưa đọc

✅ **Tính năng nâng cao**
- Emoji picker tùy chỉnh
- Chọn và gửi ảnh
- Chọn file (UI ready)
- Pagination tin nhắn cũ

✅ **Socket.IO Integration**
- Kết nối socket
- Join/leave room
- Realtime messaging
- Typing indicator

✅ **Redux State Management**
- Chat reducer
- Actions và hooks
- Persistent state

## Tính năng cần phát triển

🔄 **Backend Integration**
- Kết nối API thực
- Database schema
- Authentication

🔄 **File Upload**
- Upload và hiển thị file
- Preview file types
- Download file

🔄 **Push Notifications**
- Thông báo tin nhắn mới
- Background notifications

🔄 **Tính năng nâng cao**
- Tìm kiếm tin nhắn
- Forward tin nhắn
- Reply tin nhắn
- Reaction emoji

## Cách test

1. **Chạy ứng dụng**
   ```bash
   npm start
   npm run android # hoặc ios
   ```

2. **Truy cập Chat Demo**
   - Mở app → Home → "Chat Demo"
   - Nhấn "Tải lại dữ liệu demo"

3. **Test các chức năng**
   - Xem danh sách chat
   - Mở phòng chat và gửi tin nhắn
   - Test emoji picker
   - Test chọn ảnh
   - Tạo nhóm chat mới

4. **Kiểm tra hệ thống**
   - Nhấn "Test thư viện chat" để kiểm tra tất cả dependencies
   - Nhấn "Kiểm tra quyền" để xem trạng thái permissions
   - Kiểm tra console logs để debug

## Troubleshooting

### Lỗi Socket connection
- Kiểm tra URL socket server
- Kiểm tra token authentication

### Lỗi Image picker
- Kiểm tra permissions camera/gallery
- Kiểm tra cấu hình Android/iOS

### Lỗi Navigation
- Kiểm tra route names trong router.tsx
- Kiểm tra navigation stack

## Liên hệ

Nếu có vấn đề hoặc cần hỗ trợ, vui lòng liên hệ team phát triển.
