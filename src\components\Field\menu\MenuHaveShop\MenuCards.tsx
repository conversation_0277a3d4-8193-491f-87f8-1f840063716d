import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { AppSvg, Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../../../assets/skin/colors';
import { Text } from 'react-native-paper';
import { TypoSkin } from '../../../../assets/skin/typography';
import { useNavigation } from '@react-navigation/native';
import { Title } from '../../../../Config/Contanst';
import { RootScreen } from '../../../../router/router';

interface MenuCardsProps {
  svgIcon: string;
  color: string;
  title: string;
  order: string;
}

const MenuCards = (props: MenuCardsProps) => {
  let { svgIcon, color, title, order } = props;
  const navigation = useNavigation<any>();
  const handleNavigateOrders = (order: string) => {
    navigation.navigate(order);
  };
  return (
    <TouchableOpacity
      style={styles.actionCard}
      onPress={() => handleNavigateOrders(order)}>
      <AppSvg SvgSrc={svgIcon} size={20} />
      <Text style={styles.actionText}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 10,
    paddingVertical: 24,
    shadowColor: '#1890FF4D',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    height: 70,
    borderWidth: 0.3,
    borderColor: '#1890FF4D',
  },
  actionText: {
    ...TypoSkin.title5,
    color: ColorThemes.light.neutral_text_title_color,
    marginLeft: 10,
  },
});

export default MenuCards;
