import React, {useRef, useState} from 'react';
import {View, Text, StyleSheet, Dimensions} from 'react-native';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import FastImage from 'react-native-fast-image';
import ClickableImage from '../../../../components/ClickableImage';
import {ProductImageCarouselProps} from '../types';
import {ColorThemes} from '../../../../assets/skin/colors';

const ProductImageCarousel: React.FC<ProductImageCarouselProps> = ({
  images,
  onIndexChange,
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);
  const swiperRef = useRef<SwiperFlatList>(null);

  const handleImageIndexChange = ({index}: {index: number}) => {
    setCurrentImageIndex(index);
    onIndexChange?.(index);
  };

  const renderImageItem = ({item}: {item: string}) => (
    <ClickableImage
      key={item}
      source={{uri: item}}
      resizeMode={FastImage.resizeMode.cover}
      style={styles.productImage}
    />
  );

  return (
    <View style={styles.imageContainer}>
      <SwiperFlatList
        ref={swiperRef}
        autoplay
        autoplayDelay={5}
        autoplayLoop
        showPagination={false}
        data={images}
        onChangeIndex={handleImageIndexChange}
        renderItem={renderImageItem}
      />
      <View style={styles.imageCounter}>
        <Text style={styles.imageCounterText}>
          {currentImageIndex + 1}/{images.length}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    width: '100%',
    height: 350,
    position: 'relative',
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  productImage: {
    width: Dimensions.get('window').width,
    height: '100%',
  },
  imageCounter: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  imageCounterText: {
    color: '#fff',
    fontSize: 12,
  },
});

export default ProductImageCarousel;
