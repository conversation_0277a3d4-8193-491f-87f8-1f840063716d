import { ChatMessage } from '../types/ChatTypes';

/**
 * Utility class to convert between database message format and GiftedChat format
 */
export class MessageConverter {
  
  /**
   * Convert database ChatMessage to GiftedChat format
   */
  static toGiftedChatFormat(dbMessage: ChatMessage): any {
    const giftedMessage: any = {
      _id: dbMessage.Id,
      text: dbMessage.Content || '',
      createdAt: new Date(dbMessage.DateCreated),
      user: {
        _id: dbMessage.user?.Id || dbMessage.CustomerId,
        name: dbMessage.user?.Name || 'User',
        avatar: dbMessage.user?.Avatar,
      },
      sent: dbMessage.sent,
      received: dbMessage.received,
    };

    // Handle different message types based on Type field
    switch (dbMessage.Type) {
      case '2': // Image
        giftedMessage.image = dbMessage.FileUrl;
        giftedMessage.text = ''; // Clear text for image messages
        break;
      case '3': // File
        giftedMessage.file = {
          url: dbMessage.FileUrl,
          name: dbMessage.Content || 'File',
        };
        giftedMessage.text = ''; // Clear text for file messages
        break;
      case '4': // Emoji
        giftedMessage.text = dbMessage.Content; // Emoji is stored in Content
        break;
      default: // Text (Type = '1' or undefined)
        giftedMessage.text = dbMessage.Content || '';
        break;
    }

    return giftedMessage;
  }

  /**
   * Convert GiftedChat message to database ChatMessage format
   */
  static toDatabaseFormat(
    giftedMessage: any, 
    customerId: string, 
    chatRoomId: string
  ): Partial<ChatMessage> {
    // Determine message type and content
    let messageType = '1'; // Default to text
    let content = giftedMessage.text || '';
    let fileUrl = '';

    if (giftedMessage.image) {
      messageType = '2'; // Image
      fileUrl = giftedMessage.image;
      content = ''; // No text content for image
    } else if (giftedMessage.file) {
      messageType = '3'; // File
      fileUrl = giftedMessage.file.url;
      content = giftedMessage.file.name || '';
    } else if (giftedMessage.text && this.isEmoji(giftedMessage.text)) {
      messageType = '4'; // Emoji (if text contains only emoji)
      content = giftedMessage.text;
    }

    return {
      Id: giftedMessage._id || Date.now().toString(),
      Content: content,
      DateCreated: giftedMessage.createdAt ? 
        (giftedMessage.createdAt instanceof Date ? 
          giftedMessage.createdAt.getTime() : 
          new Date(giftedMessage.createdAt).getTime()
        ) : new Date().getTime(),
      Type: messageType,
      FileUrl: fileUrl || undefined,
      CustomerId: customerId,
      ChatRoomId: chatRoomId,
    };
  }

  /**
   * Check if text contains only emoji characters
   */
  private static isEmoji(text: string): boolean {
    // Simple emoji detection - you can make this more sophisticated
    const emojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]+$/u;
    return emojiRegex.test(text.trim());
  }

  /**
   * Convert array of database messages to GiftedChat format
   */
  static toGiftedChatArray(dbMessages: ChatMessage[]): any[] {
    return dbMessages.map(msg => this.toGiftedChatFormat(msg));
  }

  /**
   * Get message type description
   */
  static getMessageTypeDescription(type: string): string {
    switch (type) {
      case '1': return 'Text';
      case '2': return 'Image';
      case '3': return 'File';
      case '4': return 'Emoji';
      default: return 'Unknown';
    }
  }

  /**
   * Check if message has file attachment
   */
  static hasFileAttachment(message: ChatMessage): boolean {
    return ['2', '3'].includes(message.Type) && !!message.FileUrl;
  }

  /**
   * Get file extension from FileUrl
   */
  static getFileExtension(fileUrl: string): string {
    if (!fileUrl) return '';
    const parts = fileUrl.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
  }

  /**
   * Check if file is image based on extension
   */
  static isImageFile(fileUrl: string): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const extension = this.getFileExtension(fileUrl);
    return imageExtensions.includes(extension);
  }
}
