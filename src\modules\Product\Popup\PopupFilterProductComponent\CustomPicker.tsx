import React, {useState} from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  FlatList,
} from 'react-native';
import {SelectableItem} from './types';

// Interface for CustomPicker
interface CustomPickerProps {
  label: string;
  data: SelectableItem[];
  selectedValue: string | null;
  onSelect: (value: string) => void;
  modalTitle: string;
}

// Internal SelectionModal component
const SelectionModal = ({
  visible,
  title,
  data,
  selectedValue,
  onClose,
  onSelect,
}: {
  visible: boolean;
  title: string;
  data: SelectableItem[];
  selectedValue: string | null;
  onClose: () => void;
  onSelect: (value: string) => void;
}) => (
  <Modal
    animationType="slide"
    transparent={true}
    visible={visible}
    onRequestClose={onClose}>
    <Pressable style={styles.modalBackdrop} onPress={onClose} />
    <View style={styles.selectionModalContainer}>
      <View style={styles.modalHeader}>
        <Text style={styles.modalTitle}>{title}</Text>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
      </View>
      <FlatList
        data={data}
        keyExtractor={item => item.id}
        renderItem={({item}) => (
          <TouchableOpacity
            style={styles.selectionItem}
            onPress={() => {
              onSelect(item.id);
              onClose();
            }}>
            <Text
              style={[
                styles.selectionItemText,
                selectedValue === item.id && styles.selectionItemTextActive,
              ]}>
              {item.name}
            </Text>
            {selectedValue === item.id && (
              <Text style={styles.selectionItemIcon}>✓</Text>
            )}
          </TouchableOpacity>
        )}
      />
    </View>
  </Modal>
);

const CustomPicker = ({
  label,
  data,
  selectedValue,
  onSelect,
  modalTitle,
}: CustomPickerProps) => {
  const [modalVisible, setModalVisible] = useState(false);

  return (
    <>
      <TouchableOpacity
        style={styles.pickerContainer}
        onPress={() => setModalVisible(true)}>
        <Text style={styles.pickerLabel}>{label}</Text>
        <Text style={styles.pickerIcon}>▼</Text>
      </TouchableOpacity>
      <SelectionModal
        visible={modalVisible}
        title={modalTitle}
        data={data}
        selectedValue={selectedValue}
        onClose={() => setModalVisible(false)}
        onSelect={onSelect}
      />
    </>
  );
};

const styles = StyleSheet.create({
  // CustomPicker styles
  pickerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F7F7F7',
    borderWidth: 1,
    borderColor: '#EAEAEA',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
  },
  pickerLabel: {
    fontSize: 15,
    color: '#333',
  },
  pickerIcon: {
    fontSize: 12,
    color: '#888',
  },

  // SelectionModal styles
  modalBackdrop: {
    flex: 1,
  },
  selectionModalContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    maxHeight: '50%',
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
    paddingBottom: 30,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    position: 'relative',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: 0,
    top: -5,
    backgroundColor: '#F0F0F0',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#888',
    fontWeight: 'bold',
  },
  selectionItem: {
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectionItemText: {
    fontSize: 16,
    color: '#333',
  },
  selectionItemTextActive: {
    color: '#0052FF',
    fontWeight: 'bold',
  },
  selectionItemIcon: {
    fontSize: 16,
    color: '#0052FF',
  },
});

export default CustomPicker;
