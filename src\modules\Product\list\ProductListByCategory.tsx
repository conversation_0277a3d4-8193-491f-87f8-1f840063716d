import React, {useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {useRoute} from '@react-navigation/native';
import {ColorThemes} from '../../../assets/skin/colors';
import CategoryHeader from '../../../Screen/Layout/headers/CategoryHeader';
import {categoryAction} from '../../../redux/actions/categoryAction';
import {useProductByCategoryHook} from '../../../redux/reducers/ProductByCategoryReducer';
import ListProduct from './ProductList/ListProduct';
import ScrollOption from './ProductList/ScrollOption';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import ListCategory from './ProductList/ListCategory';

export default function ProductListByCategory() {
  const productByCategoryHook = useProductByCategoryHook();
  const {newCategoryId} = useSelector(
    (state: RootState) => state.productByCategory,
  );
  const [isInit, setIsInit] = useState(false);
  const categoryId = useRoute<any>()?.params?.categoryId;

  useEffect(() => {
    productByCategoryHook.resetData();
    initData(categoryId);
  }, [categoryId]);

  useEffect(() => {
    if (newCategoryId) {
      categoryChange(newCategoryId);
    }
  }, [newCategoryId]);

  const initData = async (categoryId?: string) => {
    // trường hợp không có categoryId thì lấy tất cả sản phẩm
    if (!categoryId) {
      productByCategoryHook.setData('filter', {
        categoryId: null,
        brandId: null,
        activeFilters: {},
        sortOption: null,
        maxPrice: null,
        textSearch: null,
      });
    } else {
      const {childrenCategory} = await categoryChange(categoryId);

      // trường hợp có categoryId thì lấy sản phẩm theo categoryId
      productByCategoryHook.setData('filter', {
        categoryId:
          categoryId +
          ' | ' +
          childrenCategory.map((category: any) => category.Id).join(' | '),
        brandId: null,
        activeFilters: {},
        sortOption: null,
        maxPrice: null,
        textSearch: null,
      });
    }
    setIsInit(true);
  };

  const categoryChange = async (categoryId: string) => {
    const currentCategory = await categoryAction.findOne(categoryId);
    let parentCategory = null;
    let childrenCategory = [];
    if (currentCategory.ParentId) {
      parentCategory = await categoryAction.findOne(currentCategory.ParentId);
      childrenCategory = await categoryAction.find({
        searchRaw: `@ParentId:{${currentCategory.ParentId}}`,
      });
    } else {
      parentCategory = currentCategory;
      childrenCategory = await categoryAction.find({
        searchRaw: `@ParentId:{${currentCategory.Id}}`,
      });
    }

    childrenCategory.unshift({
      Id: 'all',
      Name: 'Tất cả',
      ParentId: currentCategory.ParentId,
    });

    productByCategoryHook.setData('parentCategory', parentCategory);
    productByCategoryHook.setData('childrenCategory', childrenCategory);
    productByCategoryHook.setData('currentCategory', currentCategory);

    return {childrenCategory};
  };

  return (
    <View style={styles.container}>
      <CategoryHeader />
      <ScrollOption />
      <View style={{marginHorizontal: 16}}>
        <ListCategory />
      </View>
      {isInit && <ListProduct />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  productList: {
    padding: 16,
    gap: 16,
  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
  },
  footerText: {
    marginLeft: 8,
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF6B6B',
    textAlign: 'center',
  },
});
