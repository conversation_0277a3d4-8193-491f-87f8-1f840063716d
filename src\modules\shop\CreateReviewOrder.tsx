/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';
import HeaderShop from '../../components/shop/HeaderShop';
import {useNavigation, useRoute} from '@react-navigation/native';
import {NumberStatusIcon, Title} from '../../Config/Contanst';
import CreateReviewOrderDetail from '../../components/Order/CreateReviewOrder';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';

const CreateReviewOrder = () => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <InforHeader title={Title.CreateReviewOrder} />
      <CreateReviewOrderDetail />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },

  navigator: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#00FFFF',
    paddingBottom: 18,
    borderBottomWidth: 0.5,
  },
  orderInfo: {
    display: 'flex',
    marginLeft: 13,
  },
  title: {
    fontSize: 20,
  },
  numberOrder: {
    fontSize: 15,
    marginTop: 10,
    color: '#999',
  },
});

export default CreateReviewOrder;
