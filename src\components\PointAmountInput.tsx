import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {Text} from 'react-native-paper';
import {ColorThemes} from '../assets/skin/colors';
import {TypoSkin} from '../assets/skin/typography';
import {TransactionType} from '../Config/Contanst';

interface PointAmountInputProps {
  currentPoints: number;
  transferAmount: string;
  onAmountChange: (amount: string) => void;
  type: TransactionType;
}

const PointAmountInput: React.FC<PointAmountInputProps> = ({
  currentPoints,
  transferAmount,
  onAmountChange,
  type,
}) => {
  const defaultQuickAmounts = [100, 200, 500, 1000, 5000, 10000];

  const handleQuickSelect = (amount: number) => {
    onAmountChange(amount.toString());
  };

  // Tính năng gợi ý thông minh
  const getSmartSuggestions = () => {
    const currentAmount = parseInt(transferAmount || '0');

    if (!transferAmount || transferAmount === '0') {
      return defaultQuickAmounts.filter(amount => amount <= currentPoints);
    }

    const suggestions = [];
    const baseAmount = currentAmount;

    // Gợi ý làm tròn
    if (baseAmount < 1000) {
      suggestions.push(Math.ceil(baseAmount / 100) * 100); // Làm tròn đến hàng trăm
      suggestions.push(Math.ceil(baseAmount / 500) * 500); // Làm tròn đến 500
    } else if (baseAmount < 10000) {
      suggestions.push(Math.ceil(baseAmount / 1000) * 1000); // Làm tròn đến hàng nghìn
      suggestions.push(Math.ceil(baseAmount / 5000) * 5000); // Làm tròn đến 5000
    } else {
      suggestions.push(Math.ceil(baseAmount / 10000) * 10000); // Làm tròn đến hàng vạn
      suggestions.push(Math.ceil(baseAmount / 50000) * 50000); // Làm tròn đến 50000
    }

    // Thêm một số gợi ý khác
    suggestions.push(baseAmount * 2); // Gấp đôi
    suggestions.push(Math.floor(baseAmount * 10)); // Tăng x10

    // Lọc và sắp xếp
    const uniqueSuggestions = [...new Set(suggestions)]
      .filter(
        amount =>
          amount > baseAmount &&
          amount <= currentPoints &&
          amount !== baseAmount,
      )
      .sort((a, b) => a - b)
      .slice(0, 6); // Chỉ lấy 6 gợi ý

    return uniqueSuggestions.length > 0
      ? uniqueSuggestions
      : defaultQuickAmounts.filter(amount => amount <= currentPoints);
  };

  const smartSuggestions = getSmartSuggestions();

  const formatNumber = (num: number) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const formatInputValue = (value: string) => {
    // Remove all non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');
    // Format with commas
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const handleInputChange = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    onAmountChange(numericValue);
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={styles.container}>
        <Text style={styles.label}>
          Point {type === TransactionType.tranfer ? 'chuyển' : 'rút'}
        </Text>

        <View style={styles.inputContainer}>
          <TextInput
            value={formatInputValue(transferAmount)}
            onChangeText={handleInputChange}
            placeholder="0"
            keyboardType="numeric"
            style={styles.input}
            returnKeyType="done"
            onSubmitEditing={Keyboard.dismiss}
          />
          <TouchableOpacity
            style={styles.maxButton}
            onPress={() => onAmountChange(currentPoints.toString())}>
            <Text style={styles.maxButtonText}>MAX</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.quickSelectContainer}>
          {smartSuggestions.map(amount => (
            <TouchableOpacity
              key={amount}
              style={styles.quickSelectButton}
              onPress={() => handleQuickSelect(amount)}
              activeOpacity={0.7}>
              <Text style={styles.quickSelectText}>{formatNumber(amount)}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.currentPointsContainer}>
          <Text style={styles.currentPointsLabel}>CPoint của tôi</Text>
          <Text style={styles.currentPointsValue}>
            {formatNumber(currentPoints)}
          </Text>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  label: {
    ...TypoSkin.label3,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 12,
  },
  inputContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  input: {
    backgroundColor: ColorThemes.light.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_lighter_border_color,
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingRight: 60,
    fontSize: 16,
    fontWeight: '400',
    color: ColorThemes.light.neutral_text_title_color,
    textAlign: 'left',
  },
  maxButton: {
    position: 'absolute',
    right: 12,
    top: '40%',
    transform: [{translateY: -12}],
    backgroundColor: '#FFC043',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  maxButtonText: {
    ...TypoSkin.label4,
    color: '#DA251D',
    fontWeight: '600',
    fontSize: 12,
  },
  quickSelectContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  quickSelectButton: {
    backgroundColor: ColorThemes.light.primary_background,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: ColorThemes.light.primary_border_color,
  },
  quickSelectText: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.primary_main_color,
    fontSize: 12,
  },
  currentPointsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  currentPointsLabel: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 14,
  },
  currentPointsValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.primary_main_color,
  },
});

export default PointAmountInput;
