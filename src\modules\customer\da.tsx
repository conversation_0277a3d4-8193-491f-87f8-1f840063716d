import {DataController} from '../../base/baseController';
import {FollowStatus, StorageContanst} from '../../Config/Contanst';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import {randomGID} from '../../utils/Utils';

export class CustomerDA {
  private customerController: DataController;
  private followerController: DataController;
  private orderDetailController: DataController;
  private addressController: DataController;

  constructor() {
    this.customerController = new DataController('Customer');
    this.followerController = new DataController('Customer_Follower');
    this.orderDetailController = new DataController('OrderDetail');
    this.addressController = new DataController('Address');
  }
  private customerCache: Map<string, any> = new Map();

  async getCustomerName(id: string) {
    if (this.customerCache.has(id)) {
      return this.customerCache.get(id).name || '';
    }
    const customerResult = await this.customerController.getById(id);
    if (customerResult.code === 200) {
      this.customerCache.set(id, customerResult.data);
      return customerResult.data.name;
    }
    return '';
  }
  async getCustomerItem(id: string) {
    const customerResult = await this.customerController.getById(id);
    if (customerResult.code === 200) {
      return customerResult.data;
    }
    return null;
  }

  async getCustomerbyId(id: string) {
    if (this.customerCache.has(id)) {
      return {code: 200, data: this.customerCache.get(id)};
    }
    const customerResult = await this.customerController.getById(id);
    if (customerResult.code === 200) {
      this.customerCache.set(id, customerResult.data);
      return customerResult;
    }
    return null;
  }
  async getCustomersByIds(ids: string[]) {
    if (ids.length === 0) {
      return {code: 200, data: []};
    }
    const cachedResults = ids
      .filter(id => this.customerCache.has(id))
      .map(id => this.customerCache.get(id));
    const missingIds = ids.filter(id => !this.customerCache.has(id));
    if (missingIds.length === 0) {
      return {code: 200, data: cachedResults};
    }
    const customerResult = await this.customerController.getListSimple({
      query: `@Id: {${missingIds.join(' | ')}}`,
    });

    if (customerResult.code === 200) {
      customerResult.data.forEach((item: any) =>
        this.customerCache.set(item.Id, item),
      );
      return {code: 200, data: [...cachedResults, ...customerResult.data]};
    }
    return null;
  }

  async getFollowCustomer(id: string) {
    const followingResult = await this.followerController.getListSimple({
      query: `@CustomerId: (${id}) @Status:[1]`,
    });
    const followerResult = await this.followerController.getListSimple({
      query: `@Following: (${id}) @Status:[1]`,
    });
    if (followingResult.code === 200 && followerResult.code === 200) {
      return {
        following: followingResult.data || [],
        follower: followerResult.data || [],
      };
    }
    return null;
  }

  //lấy danh sách bạn bè và những người đang gửi yêu cầu kết bạn cho mình
  async getListFriend(
    Id: string,
    page?: number,
    size?: number,
    search?: string,
  ) {
    // var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    //(@CustomerId: (${Id}) @Status:[${FollowStatus.Pending}]) <- mình gửi kết bạn cho ng khác và trạng thái đang pending
    // lấy danh sách bạn bè của người dùng trường hợp mình gửi yêu cầu kết bạn và đã xác nhận thành công, người khác gửi yêu cầu đến mình và đã xác nhận thành công, người khác gửi yêu cầu đến mình và đang chờ xác nhận
    var query = `(@CustomerId: (${Id}) @Status:[${FollowStatus.Accept}]) | (@Following: (${Id}) @Status:[${FollowStatus.Accept}]) | (@Following: (${Id}) @Status:[${FollowStatus.Pending}])`;
    const followingResult = await this.followerController.getListSimple({
      query: query,
    });
    if (followingResult.code === 200) {
      const listfollow = followingResult.data.map(
        (item: any) => item.Following,
      );
      const listcustomer = followingResult.data.map(
        (item: any) => item.CustomerId,
      );
      var listId = [...listfollow, ...listcustomer];
      listId = [...new Set(listId)].filter((item: any) => item !== Id);
      var querySearch = `@Id: {${listId.join(' | ')}}`;
      if (search) querySearch += ` @Name:(*${search ?? ''}*)`;
      const customerResult = await this.customerController.getListSimple({
        query: querySearch,
      });
      if (customerResult.code === 200) {
        const listUpdate = customerResult.data.map((item: any) => {
          return {
            ...item,
            Status: followingResult.data.find(
              (a: any) => a.Following === item.Id || a.CustomerId === item.Id,
            )?.Status,
          };
        });
        return listUpdate;
      }
    }
    return null;
  }
  //lấy danh sách đã là bạn bè

  async checkFollowCustomer(id: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const followingResult = await this.followerController.getListSimple({
      query: `(@CustomerId: (${cusId}) | @Following:(${cusId})) (@Following:(${id}) | @CustomerId:(${id}))`,
      size: 1,
    });
    if (followingResult.code === 200 && followingResult.data?.length > 0) {
      return followingResult.data[0];
    }
    return null;
  }

  async follow(id: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const data = {
        Id: randomGID(),
        CustomerId: cusId,
        Following: id,
        DateCreated: new Date().getTime(),
        Status: FollowStatus.Pending,
      };
      const customerResult = await this.followerController.add([data]);
      if (customerResult.code === 200) {
        return data;
      }
    }
    return null;
  }
  async Acceptfollow(id: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const IdFollow = await this.followerController.getListSimple({
        page: 1,
        size: 1,
        query: `@CustomerId: (${id}) @Following:(${cusId})`,
      });
      if (IdFollow?.data?.length > 0) {
        const customerResult = await this.followerController.edit([
          {
            ...IdFollow?.data[0],
            Status: FollowStatus.Accept,
          },
        ]);
        if (customerResult.code === 200) {
          return true;
        }
      }
    }
    return false;
  }

  async unfollow(id: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const fl = await this.followerController.getListSimple({
        query: `@CustomerId: (${cusId}) @Following:(${id})`,
      });
      if (fl?.data) {
        const customerResult = await this.followerController.delete([
          fl.data[0].Id,
        ]);
        if (customerResult.code === 200) {
          return true;
        }
      }
    }
    return false;
  }
  //lấy danh sách đơn hàng
  async getPurchaseHistory(page: number, size: number, searchText?: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      let query = `@CustomerId: {${cusId}}`;

      // Thêm điều kiện tìm kiếm nếu có
      if (searchText && searchText.trim() !== '') {
        // Tìm kiếm theo tên khóa học
        const courseController = new DataController('Course');
        const courseResult = await courseController.getListSimple({
          query: `@Name: (*${searchText}*)`,
          returns: ['Id'],
        });

        if (courseResult.code === 200 && courseResult.data.length > 0) {
          const courseIds = courseResult.data.map((course: any) => course.Id);
          query += ` @CourseId: {${courseIds.join(' | ')}}`;
        } else {
          // Nếu không tìm thấy khóa học nào, trả về danh sách trống
          return {code: 200, data: []};
        }
      }

      const orderDetailResult = await this.orderDetailController.getListSimple({
        page: page,
        size: size,
        query: query,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });

      if (orderDetailResult.code === 200) {
        return orderDetailResult;
      }
    }
    return null;
  }
}
