/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {FDialog, FLoading} from 'wini-mobile-components';
import {
  useNavigation,
  useRoute,
  useFocusEffect,
} from '@react-navigation/native';
import {ScrollView} from 'react-native-gesture-handler';
import UserInfo from '../../components/shop/UserInfo';
import NotShop from '../../components/shop/NotShop';
import HaveShop from '../../components/shop/HaveShop';
import {TypeMenuShop} from '../../Config/Contanst';
import MenuShop from '../../components/shop/MenuShop';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {useDispatch, useSelector} from 'react-redux';
import {ProductActions} from '../../redux/reducers/ProductReducer';
import TreeAffiliate from './treeAffiliate';
import Profile from '../customer/profile';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {ColorThemes} from '../../assets/skin/colors';
import {RootScreen} from '../../router/router';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {RootState} from '../../redux/store/store';

const Shop = () => {
  const route = useRoute<any>();
  const [select, setSelect] = useState(
    route?.params?.select ? route?.params?.select : TypeMenuShop.User,
  );
  const [numberOrder, setNumberOrder] = useState<number>(0);
  const shopInfo = useSelectorShopState().data;
  const {data: OrderData} = useSelector((state: RootState) => state.order);
  const [shop, setShop] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const dialogRef = useRef<any>(null);
  const dispatch = useDispatch<any>();
  //navi
  const navigation = useNavigation<any>();
  useEffect(() => {
    if (select == TypeMenuShop.Shop && shopInfo) {
      dispatch(ProductActions.getInforProduct(shopInfo[0]?.Id));
      setShop(shopInfo);
    }
    if (select === TypeMenuShop.Wallet) {
      navigation.push(RootScreen.MyWalletProfile);
    }
  }, [select, shopInfo]);
  useEffect(() => {
    if (OrderData) {
      setNumberOrder(OrderData?.NewOrder?.number ?? 0);
    }
  }, [OrderData]);

  // Tự động cập nhật numberOrder khi focus vào màn hình
  useFocusEffect(
    React.useCallback(() => {
      if (OrderData) {
        setNumberOrder(OrderData?.NewOrder?.number ?? 0);
      }
    }, [OrderData]),
  );
  return (
    <View style={styles.container}>
      <InforHeader title={select} showAction showBack={false} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{flex: 1, width: '100%'}}>
        <FDialog ref={dialogRef} />
        <FLoading visible={loading} />
        <UserInfo />
        <MenuShop
          select={select}
          setSelect={setSelect}
          numberOrder={numberOrder}
        />
        {select == TypeMenuShop.Shop && shop && shop.length > 0 ? (
          <HaveShop shop={shop} />
        ) : select == TypeMenuShop.Afiliate ? (
          <TreeAffiliate />
        ) : select == TypeMenuShop.User ? (
          <Profile select={select} />
        ) : (
          <NotShop />
        )}
        <View style={{height: 85}} />
      </ScrollView>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },

  navigator: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#00FFFF',
    paddingBottom: 18,
    borderBottomWidth: 0.5,
  },

  icon: {
    width: 24,
    height: 24,
  },
});

export default Shop;
