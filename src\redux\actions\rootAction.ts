// đ<PERSON><PERSON> là nơi chứa các action cần thiết cho các module

import { DataController } from '../../base/baseController';
import ConfigAPI from '../../Config/ConfigAPI';

const getImage = async ({items}: {items: any}) => {
  const url = ConfigAPI.urlImg;

  for (const item of items) {
    if (item.Img) {
      item.Img = `${url}${item.Img}`;
    }
  }

  return items;
};



export {getImage};
