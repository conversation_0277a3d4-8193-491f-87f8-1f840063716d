import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {DescriptionProps} from '../dto/dto';
import {ImageOrVideo} from 'react-native-image-crop-picker';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../svg/icon';
import FastImage from 'react-native-fast-image';

const DescriptionImage = (props: DescriptionProps) => {
  const {image, pickerImg, avataProduct, checkImage, deleteImage} = props;

  return (
    <View style={styles.section}>
      <Text style={styles.label}>
        Hình ảnh sản phẩm * ({checkImage?.length ? checkImage?.length : 0}/5)
      </Text>
      <ScrollView
        style={{
          display: 'flex',
          gap: 2,
          flexDirection: 'row',
          flexWrap: 'wrap',
        }}
        horizontal={true}>
        {image &&
          image.length > 0 &&
          image.map((item, index) => (
            <View key={index} style={{position: 'relative'}}>
              <View style={{marginTop: 10}}>
                <FastImage
                  source={{uri: item.path}}
                  style={{
                    height: 73,
                    width: 73,
                    marginLeft: 10,
                    marginRight: 10,
                  }}
                />
              </View>
              <TouchableOpacity
                onPress={() => deleteImage(index)}
                style={{
                  position: 'absolute',
                  bottom: 0,
                  left: '50%',
                  transform: [{translateX: -10}, {translateY: 1}],
                }}>
                <AppSvg SvgSrc={iconSvg.exit} size={24} />
              </TouchableOpacity>
            </View>
          ))}
      </ScrollView>
      <TouchableOpacity style={styles.imagePlaceholder} onPress={pickerImg}>
        <Text style={styles.placeholderText}>Thêm ảnh</Text>
      </TouchableOpacity>
      <View style={{marginTop: 10, marginLeft: 10, width: 73}}>
        {avataProduct && (
          <View>
            <Image
              source={{uri: avataProduct}}
              style={{height: 73, width: 73}}
            />
            <Text style={{marginTop: 10, margin: 'auto'}}>Ảnh đại diện</Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginTop: 10,
    marginLeft: 10,
    marginRight: 10,
    backgroundColor: 'white',
    borderRadius: 5,
    padding: 10,
    elevation: 2, // Tạo bóng cho Android
    shadowColor: '#A9A9A9', // Tạo bóng cho iOS
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#333',
  },
  imagePlaceholder: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderStyle: 'dashed',
    width: 73,
    height: 73,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
    marginTop: 10,
    marginLeft: 10,
  },
  placeholderText: {
    color: '#888',
    fontSize: 14,
  },
  input: {
    borderRadius: 5,
    padding: 10,
    fontSize: 14,
    color: '#555555',
  },
});

export default DescriptionImage;
