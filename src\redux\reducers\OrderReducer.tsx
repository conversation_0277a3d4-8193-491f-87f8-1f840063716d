import {useNavigation} from '@react-navigation/native';
import {
  Dispatch,
  PayloadAction,
  UnknownAction,
  createSlice,
} from '@reduxjs/toolkit';
import {OrderDA} from '../../modules/order/orderDA';
import {useSelectorCustomerState} from '../hook/customerHook';
import {StatusOrder} from '../../Config/Contanst';

interface OrderSimpleResponse {
  data?: any;
  onLoading?: boolean;
  type?: string;
}

const initState: OrderSimpleResponse = {
  data: undefined,
  onLoading: false,
};

export const orderSlice = createSlice({
  name: 'Order',
  initialState: initState,
  reducers: {
    handleActionsOrder: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETINFORORDER':
          state.data = action.payload.data;
          break;
        case 'UPDATE':
          state.data = action.payload.data;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    onFetching: state => {
      state.onLoading = true;
    },
  },
});

export const {handleActionsOrder, onFetching} = orderSlice.actions;

export default orderSlice.reducer;

export class OrderActions {
  static getInforOrder = (ShopID: string) => async (dispatch: Dispatch) => {
    // Dispatch onFetching action trước khi bắt đầu
    dispatch(onFetching());

    const orderDA = new OrderDA();
    let data = {
      NewOrder: {
        number: 0,
        data: [] as any[],
      },
      ProcessOrder: {
        number: 0,
        data: [] as any[],
      },
      DoneOrder: {
        number: 0,
        data: [] as any[],
      },
      CancelOrder: {
        number: 0,
        data: [] as any[],
      },
    };

    try {
      // Chỉ gọi API một lần để lấy tất cả orders
      const allOrders = await orderDA.getAllOrdersByShopId(ShopID);

      if (allOrders && allOrders?.code == 200 && allOrders.data) {
        const sortedOrders = [...allOrders.data].sort(
          (a: any, b: any) =>
            new Date(b.DateCreated).getTime() -
            new Date(a.DateCreated).getTime(),
        );
        // Lọc orders theo trạng thái
        const newOrders = sortedOrders.filter(
          (order: any) => order?.Status === StatusOrder.new,
        );
        const processOrders = sortedOrders.filter(
          (order: any) => order?.Status === StatusOrder.proccess,
        );
        const doneOrders = sortedOrders.filter(
          (order: any) => order?.Status === StatusOrder.success,
        );
        const cancelOrders = sortedOrders.filter(
          (order: any) => order?.Status === StatusOrder.cancel,
        );

        // Cập nhật data
        data.NewOrder.number = newOrders.length;
        data.NewOrder.data = newOrders;

        data.ProcessOrder.number = processOrders.length;
        data.ProcessOrder.data = processOrders;

        data.DoneOrder.number = doneOrders.length;
        data.DoneOrder.data = doneOrders;

        data.CancelOrder.number = cancelOrders.length;
        data.CancelOrder.data = cancelOrders;
      }

      dispatch(
        handleActionsOrder({
          type: 'GETINFORORDER',
          data: data,
        }),
      );
      console.log('check-data', data);
    } catch (error) {
      console.error('Error fetching order data:', error);
      // Dispatch với data rỗng nếu có lỗi
      dispatch(
        handleActionsOrder({
          type: 'GETINFORORDER',
          data: data,
        }),
      );
    }
  };
}
