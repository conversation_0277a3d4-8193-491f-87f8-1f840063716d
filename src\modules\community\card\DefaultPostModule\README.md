# DefaultPostModule

Module này chứa các component và utilities cho DefaultPost component.

## Cấu trúc

### types.ts

- Định nghĩa các interface và types cho component
- `DefaultPostProps`: Props chính cho DefaultPost component
- `PostData`: Cấu trúc dữ liệu bài viết
- `ListItem`, `TagItem`: Các interface cho list items và tags

### utils.ts

- Các utility functions:
  - `extractVideoId`: Trích xuất video ID từ YouTube URL
  - `truncateHtml`: Cắt HTML an toàn với xử lý các thẻ
  - `defaultImageProps`: Props mặc định cho FastImage

### styles.ts

- Tất cả StyleSheet definitions:
  - `stylesDefault`: Styles chung
  - `imageStyles`: Styles cho các component ảnh
  - `contentStyles`: Styles cho HTML content

### ImageComponents.tsx

- Các component để render ảnh:
  - `SingleImage`: Hiển thị 1 ảnh
  - `TwoImages`: Layout 2 ảnh
  - `ThreeImages`: Layout 3 ảnh
  - `MultipleImages`: Layout 4+ ảnh với overlay

### HTMLContent.tsx

- Component render nội dung HTML với tính năng:
  - Xem thêm/Thu gọn cho nội dung dài
  - Tối ưu hiệu suất với memoization
  - Xử lý styles và DOM visitors

### PostSubComponents.tsx

- Các component phụ:
  - `renderListItems`: Render danh sách items
  - `renderSeeMoreButton`: Nút xem thêm
  - `renderTags`: Render tags

### SkeletonPostCard.tsx

- Component skeleton loading cho bài viết

### index.ts

- Export tất cả modules để dễ dàng import

## Sử dụng

```typescript
import {
  DefaultPostProps,
  extractVideoId,
  // ... các exports khác
} from './DefaultPostModule';
```
