import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';

interface CustomSwitchProps {
  isOn?: boolean;
  onPress?: () => void;
  disabled?: boolean;
}

const CustomSwitch: React.FC<CustomSwitchProps> = ({
  isOn = true,
  onPress,
  disabled = false,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.switchContainer,
        isOn && styles.switchOn,
        disabled && styles.switchDisabled,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={disabled}>
      <Text style={styles.switchText}>{isOn ? 'ON' : 'OFF'}</Text>
      <View style={[styles.switchCircle, isOn && styles.switchCircleOn]} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  switchContainer: {
    width: 70,
    height: 30,
    borderRadius: 16,
    backgroundColor: '#ccc',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
    justifyContent: 'flex-start',
  },
  switchOn: {
    backgroundColor: '#1890ff',
    justifyContent: 'flex-end',
  },
  switchDisabled: {
    opacity: 0.5,
  },
  switchText: {
    color: '#fff',
    fontWeight: 'bold',
    marginHorizontal: 8,
    zIndex: 1,
    marginRight: 15,
    marginLeft: 10,
  },
  switchCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    position: 'absolute',
    right: 4,
  },
  switchCircleOn: {
    left: 4,
    right: undefined,
  },
});

export default CustomSwitch;
