/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {AppButton, FDialog} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import {ProductDA} from './productDA';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {RootScreen} from '../../router/router';
import ProductCarousel from '../../components/ProductCarousel';
import {AppDispatch} from '../../redux/store/store';
import {useDispatch} from 'react-redux';
import {CartActions} from '../../redux/reducers/CartReducer';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  id: string;
  onPressSeeMore?: () => void;
  onRefresh?: boolean;
}

export default function ProductBestSeller(props: Props) {
  const [data, setData] = useState<Array<any>>([]);
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  const productDA = new ProductDA();
  const dispatch: AppDispatch = useDispatch();

  useEffect(() => {
    getData();
  }, [props.onRefresh]);

  const getData = async () => {
    const data = await productDA.getProductBestSeller();
    setData(data);
  };

  return (
    <View style={{marginTop: 20}}>
      <FDialog ref={dialogRef} />
      {props.titleList ? (
        <View style={styles.titleContainer}>
          <Text style={styles.titleText}>{props.titleList}</Text>
          {props.isSeeMore ? (
            <AppButton
              title={'Xem thêm'}
              containerStyle={styles.seeMoreButtonContainer}
              backgroundColor={'transparent'}
              textStyle={styles.seeMoreButtonText}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={props.onPressSeeMore}
              textColor={ColorThemes.light.infor_main_color}
            />
          ) : null}
        </View>
      ) : null}
      {data ? (
        <ProductCarousel
          title={'Sản phẩm bán chạy'}
          // title={props.titleList}
          products={data}
          onSeeAll={props.onPressSeeMore}
          onProductPress={item => {
            navigation.push(RootScreen.ProductDetail, {id: item.Id});
          }}
          onAddToCart={item => {
            //TODO: add to cart
            dispatch(CartActions.addItemToCart(item, 1));
          }}
          onFavoritePress={item => {
            //TODO: add to favorite
          }}
        />
      ) : (
        <></>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  titleText: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  seeMoreButtonContainer: {
    justifyContent: 'flex-start',
    alignSelf: 'baseline',
  },
  seeMoreButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.infor_main_color,
  },
});
