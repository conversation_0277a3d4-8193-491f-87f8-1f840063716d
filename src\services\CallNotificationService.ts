import WebRTCService from '../features/call/WebRTCService';
import { showSnackbar, ComponentStatus } from 'wini-mobile-components';
import IncomingCallOverlayService from './IncomingCallOverlayService';

interface IncomingCallData {
  from: string;
  fromName?: string;
  fromAvatar?: string;
}

class CallNotificationService {
  private static instance: CallNotificationService;
  private isInitialized = false;

  private constructor() {}

  static getInstance(): CallNotificationService {
    if (!CallNotificationService.instance) {
      CallNotificationService.instance = new CallNotificationService();
    }
    return CallNotificationService.instance;
  }

  initialize() {
    if (this.isInitialized) {
      return;
    }

    // Setup WebRTC socket listeners
    WebRTCService.setupSocketListeners();

    // Thiết lập callback cho WebRTC để xử lý incoming calls
    WebRTCService.setCallbacks({
      onCallReceived: this.handleIncomingCall.bind(this),
      onCallAccepted: this.handleCallAccepted.bind(this),
      onCallRejected: this.handleCallRejected.bind(this),
      onCallEnded: this.handleCallEnded.bind(this),
      onError: this.handleCallError.bind(this),
    });

    this.isInitialized = true;
    console.log('✅ CallNotificationService initialized');
  }

  private handleIncomingCall(data: IncomingCallData) {
    console.log('📞 Incoming call from:', data.from);

    // Hiển thị incoming call notification
    IncomingCallOverlayService.showIncomingCall({
      callerName: data.fromName || 'Người dùng',
      callerAvatar: data.fromAvatar,
      callerId: data.from,
    });
  }



  private handleCallAccepted(data: { from: string }) {
    console.log('✅ Call accepted by:', data.from);
    showSnackbar({
      status: ComponentStatus.SUCCSESS,
      message: 'Cuộc gọi đã được chấp nhận',
    });
  }

  private handleCallRejected(data: { from: string }) {
    console.log('❌ Call rejected by:', data.from);
    showSnackbar({
      status: ComponentStatus.WARNING,
      message: 'Cuộc gọi bị từ chối',
    });
  }

  private handleCallEnded(data: { from: string }) {
    console.log('📞 Call ended by:', data.from);
    showSnackbar({
      status: ComponentStatus.INFOR,
      message: 'Cuộc gọi đã kết thúc',
    });
  }

  private handleCallError(error: any) {
    console.error('📞 Call error:', error);
    showSnackbar({
      status: ComponentStatus.ERROR,
      message: 'Có lỗi xảy ra trong cuộc gọi',
    });
  }

  // Phương thức để hiển thị notification khi app ở background
  showIncomingCallNotification(data: IncomingCallData) {
    // TODO: Implement push notification for incoming calls when app is in background
    // Có thể sử dụng react-native-push-notification hoặc @react-native-firebase/messaging
    console.log('📱 Should show push notification for incoming call:', data);
  }

  // Cleanup khi app bị đóng
  cleanup() {
    this.isInitialized = false;
    console.log('🧹 CallNotificationService cleaned up');
  }
}

export default CallNotificationService.getInstance();
