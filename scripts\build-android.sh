#!/bin/bash

# <PERSON><PERSON><PERSON> để build và test ứng dụng Android với chức năng chat

echo "🚀 Building Android app with Chat features..."

# Kiểm tra Node.js và npm
echo "📋 Checking prerequisites..."
node --version
npm --version

# Cài đặt dependencies
echo "📦 Installing dependencies..."
npm install

# Clean cache
echo "🧹 Cleaning cache..."
npx react-native start --reset-cache &
METRO_PID=$!
sleep 5
kill $METRO_PID

# Build Android
echo "🔨 Building Android..."
cd android
./gradlew clean
./gradlew assembleDebug
cd ..

echo "✅ Build completed!"
echo ""
echo "📱 To run the app:"
echo "   npx react-native run-android"
echo ""
echo "🧪 To test chat features:"
echo "   1. Open app"
echo "   2. Go to Home → Chat Demo"
echo "   3. Test all chat functions"
echo ""
echo "🔍 Check these features:"
echo "   ✓ Chat list screen"
echo "   ✓ Chat room with GiftedChat"
echo "   ✓ Emoji picker"
echo "   ✓ Image picker (camera/gallery)"
echo "   ✓ File picker"
echo "   ✓ Create group chat"
echo "   ✓ Socket.IO connection"
echo "   ✓ Permissions handling"
