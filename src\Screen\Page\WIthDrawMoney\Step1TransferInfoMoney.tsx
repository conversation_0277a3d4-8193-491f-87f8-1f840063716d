import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, TextInput, TouchableOpacity } from 'react-native';
import { AppButton } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import RecipientSelector from '../../../components/RecipientSelector';
import PointAmountInput from '../../../components/PointAmountInput';
import { TransactionType } from '../../../Config/Contanst';
import { Text } from 'react-native-paper';
import { DropdownForm } from '../../../modules/Default/form/component-form';
import { useForm, Controller } from 'react-hook-form';
import CustomDropdown from '../../../components/CustomDropdown';
import { TypoSkin } from '../../../assets/skin/typography';
import ShopDA from '../../../modules/shop/da';

interface Step1TransferInfoProps {
  currentPoints: number;
  transferAmount: string;
  onNext: () => void;
  type: TransactionType;
  setGetDataStepOne: (data: any) => void;
  getDataStepOne: any;
}

interface FormData {
  accountNumber: string;
  recipientName: string;
  bank: { id: number; name: string } | null;
  amount: string;
  getDataStepOne: any;
}

const Step1TransferInfoMoney: React.FC<Step1TransferInfoProps> = ({
  currentPoints,
  transferAmount,
  onNext,
  type,
  setGetDataStepOne,
  getDataStepOne
}) => {
  const [isFulfilled, setIsFulfilled] = useState(false);
  const methods = useForm<FormData>({
    shouldFocusError: false,
    mode: 'onChange',
    defaultValues: {
      accountNumber: '',
      recipientName: '',
      bank: null,
      amount: transferAmount || ''
    }
  });

  const { control, formState: { errors, isValid }, watch, setValue, trigger } = methods;

  const formatInputValue = (value: string) => {
    // Remove all non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');
    // Format with commas
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  useEffect(() => {
    if (watch().accountNumber && watch().recipientName && watch().bank && watch().amount) {
      setIsFulfilled(true)
    } else {
      setIsFulfilled(false)
    }

  }, [watch().accountNumber && watch().recipientName && watch().bank && watch().amount])


  const handleNext = () => {
    setGetDataStepOne({
      accountNumber: watch().accountNumber,
      recipientName: watch().recipientName,
      bank: watch().bank,
      amount: watch().amount
    })
    onNext()
  }


  const formatNumber = (num: number) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }



  const validateAccountNumber = (value: string) => {
    if (!value || value.trim() === '') {
      return 'Số tài khoản không được để trống';
    }
    if (value.length < 6) {
      return 'Số tài khoản phải có ít nhất 6 ký tự';
    }
    if (!/^\d+$/.test(value)) {
      return 'Số tài khoản chỉ được chứa số';
    }
    return true;
  };

  const validateRecipientName = (value: string) => {
    if (!value || value.trim() === '') {
      return 'Tên người nhận không được để trống';
    }
    if (value.length < 2) {
      return 'Tên người nhận phải có ít nhất 2 ký tự';
    }
    if (!/^[a-zA-ZÀ-ỹ\s]+$/.test(value)) {
      return 'Tên người nhận chỉ được chứa chữ cái và khoảng trắng';
    }
    return true;
  };

  const validateAmount = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    const amount = parseInt(numericValue || '0');

    if (!value || value.trim() === '') {
      return 'Số tiền không được để trống';
    }
    if (amount <= 0) {
      return 'Số tiền phải lớn hơn 0';
    }
    if (amount > currentPoints) {
      return `Số tiền không được vượt quá ${formatNumber(currentPoints)} point`;
    }
    return true;
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Số tài khoản</Text>
          <Controller
            control={control}
            name="accountNumber"
            rules={{ validate: validateAccountNumber }}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={[styles.input, errors.accountNumber && styles.inputError]}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
                keyboardType="numeric"
                placeholder="Nhập số tài khoản"
              />
            )}
          />
          {errors.accountNumber && (
            <Text style={styles.errorText}>{errors.accountNumber.message}</Text>
          )}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Tên người nhận</Text>
          <Controller
            control={control}
            name="recipientName"
            rules={{ validate: validateRecipientName }}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={[styles.input, errors.recipientName && styles.inputError]}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
                keyboardType="default"
                placeholder="Nhập tên người nhận"
              />
            )}
          />
          {errors.recipientName && (
            <Text style={styles.errorText}>{errors.recipientName.message}</Text>
          )}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Ngân hàng</Text>
          <DropdownForm
            placeholder="Chọn ngân hàng"
            name="bank"
            control={methods.control as any}
            style={{
              borderColor: errors.bank ? '#FF4444' : '#D4E2F3',
              height: 50,
              fontSize: 20,
              color: '#000',
            }}
            errors={methods.formState.errors}
            options={[
              { id: 1, name: 'Ngân hàng Vietcombank' },
              { id: 2, name: 'Ngân hàng Techcombank' },
              { id: 3, name: 'Ngân hàng Agribank' },
              { id: 4, name: 'Ngân hàng Vietinbank' },
              { id: 5, name: 'Ngân hàng BIDV' },
              { id: 6, name: 'Ngân hàng MB' },
              { id: 7, name: 'Ngân hàng Viet Capital Bank' },
              { id: 8, name: 'Ngân hàng Vietcom Bank' },
            ]}
          />
          {errors.bank && (
            <Text style={styles.errorText}>{errors.bank.message}</Text>
          )}
        </View>

        <View style={styles.container}>
          <Text style={styles.label}>Point {type === TransactionType.tranfer ? 'chuyển' : 'rút'}</Text>

          <View style={styles.inputContainer}>
            <Controller
              control={control}
              name="amount"
              rules={{ validate: validateAmount }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  value={formatInputValue(value)}
                  onChangeText={(text) => {
                    const numericValue = text.replace(/[^0-9]/g, '');
                    onChange(numericValue);
                  }}
                  placeholder="0"
                  keyboardType="numeric"
                  style={[styles.input, errors.amount && styles.inputError]}
                />
              )}
            />
            <TouchableOpacity
              style={{
                position: 'absolute',
                right: 12,
                top: '75%',
                transform: [{ translateY: -12 }],
                backgroundColor: '#FFC043',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 12,
              }}
              onPress={() => {
                setValue('amount', currentPoints.toString());
              }}
            >
              <Text style={{
                ...TypoSkin.label4,
                color: '#DA251D',
                fontWeight: '600',
                fontSize: 12,
              }}>MAX</Text>
            </TouchableOpacity>
          </View>
          {errors.amount && (
            <Text style={styles.errorText}>{errors.amount.message}</Text>
          )}

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingTop: 16,
            borderTopWidth: 1,
            borderTopColor: ColorThemes.light.neutral_lighter_border_color,
          }}>
            <Text style={{
              ...TypoSkin.regular2,
              color: ColorThemes.light.neutral_text_subtitle_color,
              fontSize: 14,
            }}>Số dư bán hàng</Text>
            <Text style={{
              ...TypoSkin.heading6,
              color: ColorThemes.light.primary_main_color,
            }}>{formatNumber(currentPoints ? currentPoints : 0)}</Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <AppButton
          title="Tiếp theo"
          onPress={handleNext}
          disabled={!isFulfilled}
          backgroundColor={
            isFulfilled
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.neutral_lighter_border_color
          }
          containerStyle={styles.button}
          borderColor='transparent'
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  inputContainer: {
    paddingBottom: 16,
    paddingTop: 16,
  },
  label: {
    marginBottom: 8,
    fontSize: 14,
    color: '#666',
  },
  input: {
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D4E2F3',
    height: 50,
    paddingHorizontal: 16,
    fontSize: 13,
    fontFamily: 'roboto',
    color: 'black',
  },
  inputError: {
    borderColor: '#FF4444',
  },
  errorText: {
    color: '#FF4444',
    fontSize: 12,
    marginTop: 4,
    fontFamily: 'roboto',
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: ColorThemes.light.white,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  button: {
    borderRadius: 24,
    height: 48,
    width: '80%',
    margin: 'auto',
  },
});

export default Step1TransferInfoMoney
