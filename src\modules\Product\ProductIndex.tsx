import {
  View,
  RefreshControl,
  ScrollView,
  NativeScrollEvent,
} from 'react-native';
import Header<PERSON>ogo from '../../Screen/Layout/headers/HeaderLogo';
import CategoryGrid from '../category/CategoryGrid';
import {RootScreen} from '../../router/router';
import {useNavigation} from '@react-navigation/native';
import HotProductsSection from './HotProductsSection';
import FreeShipProductSection from './section/FreeShipProductSection';
import ProductBestSeller from './productBestSeller';
import SuggestionProductSection from './section/SuggestionProductSection';
import BannerSection from './section/bannerSection';
import MuchSearchSearch from './section/MuchSearchSearch';
import {ColorThemes} from '../../assets/skin/colors';
import {useState, useRef, useCallback} from 'react';
import DefaultBanner from '../banner/Banner';

const ProductIndex = () => {
  const navigation = useNavigation<any>();
  const [refreshing, setRefreshing] = useState(false);
  const [isLoadmore, setIsLoadmore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const lastScrollY = useRef(0);

  const onSeeMore = (categoryId?: string) => {
    if (categoryId) {
      navigation.navigate(RootScreen.ProductListByCategory, {
        categoryId: categoryId,
      });
    } else {
      navigation.navigate(RootScreen.ProductListByCategory);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    setHasMore(true); // Reset hasMore on refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const handleLoadMore = useCallback(() => {
    if (!isLoadmore && hasMore) {
      setIsLoadmore(true);
    }
  }, [isLoadmore, hasMore]);

  const isCloseToBottom = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }: NativeScrollEvent) => {
    const paddingToBottom = 100; // Reduced from 300 to 100
    return (
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom
    );
  };

  const handleScroll = useCallback(
    (event: any) => {
      const currentScrollY = event.nativeEvent.contentOffset.y;

      // Only process if scrolling down and close to bottom
      if (
        currentScrollY > lastScrollY.current &&
        isCloseToBottom(event.nativeEvent)
      ) {
        handleLoadMore();
      }

      lastScrollY.current = currentScrollY;
    },
    [handleLoadMore],
  );

  return (
    <View
      style={{
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <HeaderLogo />

      <ScrollView
        style={{height: '100%', width: '100%', paddingHorizontal: 16}}
        onScroll={handleScroll}
        scrollEventThrottle={16} // Changed from 200 to 16 for smoother detection
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        <View key="banner1" style={{marginVertical: 12}}>
          <BannerSection />
        </View>
        <CategoryGrid
          key="categories"
          numColumns={3}
          onCategoryPress={category => {
            onSeeMore(category.Id);
          }}
        />
        <HotProductsSection
          key="hot_products"
          pageSize={10}
          onSeeAll={onSeeMore}
          onRefresh={refreshing}
        />
        <FreeShipProductSection key="free_ship" onRefresh={refreshing} />
        <MuchSearchSearch
          key="much_search"
          onSeeMore={onSeeMore}
          onRefresh={refreshing}
        />
        <View key="banner2" style={{marginVertical: 12}}>
          <DefaultBanner type={3} position={1} />
        </View>
        <ProductBestSeller
          key="best_seller"
          isSeeMore
          id="best-selling"
          onPressSeeMore={onSeeMore}
          onRefresh={refreshing}
        />
        <SuggestionProductSection
          key="suggestions"
          onSeeAllPress={onSeeMore}
          scrollEnabled={false}
          onRefresh={refreshing}
          isLoadmore={isLoadmore}
          onLoadMoreEnd={hasMore => {
            setIsLoadmore(false);
            setHasMore(hasMore);
          }}
        />
        <View key="footer_space" style={{height: 220}} />
      </ScrollView>
    </View>
  );
};

export default ProductIndex;
