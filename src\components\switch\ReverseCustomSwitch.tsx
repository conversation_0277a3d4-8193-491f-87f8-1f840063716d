import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';

interface ReverseCustomSwitchProps {
  isOn?: boolean;
  onPress?: () => void;
  disabled?: boolean;
}

const ReverseCustomSwitch: React.FC<ReverseCustomSwitchProps> = ({
  isOn = false,
  onPress,
  disabled = false,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.switchContainer,
        isOn && styles.switchOn,
        disabled && styles.switchDisabled,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={disabled}>
      <View style={[styles.switchCircle, isOn && styles.switchCircleOn]} />
      <Text style={styles.switchText}>{isOn ? 'ON' : 'OFF'}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  switchContainer: {
    width: 70,
    height: 30,
    borderRadius: 16,
    backgroundColor: '#ccc',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
    justifyContent: 'flex-end',
  },
  switchOn: {
    backgroundColor: '#ccc',
    justifyContent: 'flex-start',
  },
  switchDisabled: {
    opacity: 0.5,
  },
  switchText: {
    color: '#000',
    fontWeight: 'bold',
    marginHorizontal: 8,
    zIndex: 1,
    marginLeft: 15,
    marginRight: 10,
  },
  switchCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    position: 'absolute',
    left: 4,
  },
  switchCircleOn: {
    right: 4,
    left: undefined,
  },
});

export default ReverseCustomSwitch;
