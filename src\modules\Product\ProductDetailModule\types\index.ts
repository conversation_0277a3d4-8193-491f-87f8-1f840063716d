// TypeScript interfaces for ProductDetail module
export interface ProductData {
  Id: string;
  Name: string;
  Price: number;
  Discount?: number;
  Sold: number;
  InStock: number;
  BrandName?: string;
  BrandId?: string;
  CategoryId: string;
  CategoryName?: string;
  Content?: string;
  IsFreeShip?: boolean;
  IsFavorite?: boolean;
  rating?: number;
  totalRate?: number;
  countRate?: number;
  ListImg?: string;
  Img?: string;
}

export interface ShopData {
  Id: string;
  Name: string;
  CustomerId: string;
  Mobile?: string;
  Img?: string;
  OwnerName?: string;
  rating?: number;
  totalRate?: number;
  countRate?: number;
  totalProducts?: number;
  totalOrder?: number;
  products?: ProductData[];
}

export interface SkeletonBoxProps {
  width: number | string;
  height: number;
  style?: any;
}

export interface ProductDetailHeaderProps {
  onBack: () => void;
  onCartPress: () => void;
  onMenuPress: () => void;
}

export interface ProductImageCarouselProps {
  images: string[];
  onIndexChange?: (index: number) => void;
}

export interface ProductPriceSectionProps {
  price?: number;
  discount?: number;
  isFavorite: boolean;
  onFavoritePress: () => void;
  onSharePress: () => void;
}

export interface ProductInfoSectionProps {
  brandName?: string;
  sold: number;
  inStock: number;
}

export interface ShippingSectionProps {
  isFreeShip?: boolean;
}

export interface RatingSectionProps {
  rating?: number;
  onViewAllPress: () => void;
}

export interface SellerInfoSectionProps {
  shop: ShopData | null;
  onPress: () => void;
}

export interface ShopInfoSectionProps {
  rating?: number;
  totalProducts?: number;
  totalOrder?: number;
  onPress: (type: 'rating' | 'product' | 'order') => void;
}

export interface RelatedProductsSectionProps {
  products?: ProductData[];
  onProductPress: (productId: string) => void;
}

export interface ProductDescriptionSectionProps {
  content?: string;
  windowWidth: number;
}

export interface BottomActionBarProps {
  onChatPress: () => void;
  onAddToCartPress: () => void;
  onBuyNowPress: () => void;
}
