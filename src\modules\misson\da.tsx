import { DataController } from "../../base/baseController";

class MissionDA {
    private missionController: DataController;
    constructor() {
        this.missionController = new DataController('Mission');
    }
    async getMission() {
        const response = await this.missionController.getListSimple({
            query: '*',
        });
        if (response?.code === 200) {
            return response;
        }
        return null;
    }
}

