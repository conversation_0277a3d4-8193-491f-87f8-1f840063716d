import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../../svg/icon';
import {ShippingSectionProps} from '../types';

const ShippingSection: React.FC<ShippingSectionProps> = ({isFreeShip}) => {
  return (
    <View style={styles.shippingContainer}>
      <AppSvg SvgSrc={iconSvg.delivery} size={20} />
      {isFreeShip ? (
        <Text style={styles.shippingFree}>Miễn phí giao hàng</Text>
      ) : (
        <Text style={styles.shippingDistance}>3.0km Free</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  shippingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  shippingDistance: {
    fontSize: 12,
    color: '#3FB993',
    marginLeft: 8,
  },
  shippingFree: {
    fontSize: 13,
    color: '#3FB993',
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default ShippingSection;
