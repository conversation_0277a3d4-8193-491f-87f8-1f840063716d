import {format, parseISO} from 'date-fns';
import {View, Image, Text, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {
  NotificationItem,
  NotificationStatus,
  NotificationType,
} from '../../../../redux/models/notification';
import dayjs from 'dayjs';
import {Divider} from 'react-native-paper';

const NOTIFICATION_CONFIG = {
  [NotificationType.GENERAL]: {
    image: require('../../../../assets/images/noti_general_icon.png'),
  },
  [NotificationType.AFFILIATE_IN]: {
    image: require('../../../../assets/images/noti_in_icon.png'),
  },
  [NotificationType.AFFILIATE_OUT]: {
    image: require('../../../../assets/images/noti_out_icon.png'),
  },
};

const formatTimestamp = (timestamp: number) => {
  if (!timestamp) {
    return '';
  }

  return dayjs(timestamp).format('HH:mm - DD/MM/YYYY');
};

interface NotificationItemProps {
  item: NotificationItem;
  showDivider: boolean;
}

const NotificationCard = ({
  item,
  showDivider = true,
}: NotificationItemProps) => {
  const config =
    NOTIFICATION_CONFIG[item.Type as keyof typeof NOTIFICATION_CONFIG] ||
    NOTIFICATION_CONFIG[1];

  return (
    <View style={styles.container}>
      <View style={styles.itemContainer}>
        <View style={[styles.iconContainer]}>
          <Image source={config.image} />
        </View>
        <View style={styles.contentContainer}>
          <Text style={styles.text}>{item.Content}</Text>
          <Text style={styles.timestamp}>
            {formatTimestamp(item.DateCreated)}
          </Text>
        </View>
        {item.Status === NotificationStatus.UNREAD && (
          <View style={styles.unreadDot} />
        )}
      </View>
      {showDivider && (
        <Divider
          style={{
            marginTop: 12,
            backgroundColor: ColorThemes.light.primary_darker_color,
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8A8A8E',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  iconContainer: {
    width: 30,
    height: '100%',
    borderRadius: 8,
    marginRight: 12,
  },
  // Style cho emoji
  emoji: {
    fontSize: 20, // Chỉnh kích thước emoji tại đây
  },
  contentContainer: {
    flex: 1,
  },
  text: {
    fontSize: 15,
    color: '#0D1C2E',
    lineHeight: 22,
    fontWeight: '500',
  },
  timestamp: {
    fontSize: 13,
    color: '#6c757d',
    marginTop: 4,
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF3B30',
    marginLeft: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#8A8A8E',
  },
});

export default NotificationCard;
