import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from 'react-native';
import {AppButton, Winicon} from 'wini-mobile-components';
import SquareProductCard from '../../components/SquareProductCard';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';

const {width} = Dimensions.get('window');

// <PERSON>ích thước của mỗi item sản phẩm
const ITEM_WIDTH = width * 0.32;
const ITEM_HEIGHT = ITEM_WIDTH * 1.5;
const ITEM_SPACING = 10;

// Tạo component ItemSeparator bên ngoài component chính
const ItemSeparator = React.memo(() => <View style={{width: ITEM_SPACING}} />);

interface HotProductsRowProps {
  title?: string;
  products: any[];
  onSeeAll?: () => void;
  onProductPress?: (product: any) => void;
  showRating?: boolean;
}

const HotProductsRow: React.FC<HotProductsRowProps> = ({
  title = 'Sản phẩm HOT',
  products,
  onSeeAll,
  onProductPress,
  showRating = true,
}) => {
  const renderItem = ({item}: {item: any}) => {
    return (
      <SquareProductCard
        item={item}
        onPress={onProductPress}
        width={ITEM_WIDTH}
        height={ITEM_HEIGHT}
        showRating={showRating}
      />
    );
  };

  return (
    <View style={{height: 330}}>
      <View style={styles.header}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <AppButton
          title={'Xem thêm'}
          containerStyle={{
            justifyContent: 'flex-start',
            alignSelf: 'baseline',
          }}
          backgroundColor={'transparent'}
          textStyle={{
            ...TypoSkin.buttonText3,
            color: ColorThemes.light.infor_main_color,
          }}
          borderColor="transparent"
          suffixIconSize={16}
          suffixIcon={'outline/arrows/circle-arrow-right'}
          onPress={onSeeAll}
          textColor={ColorThemes.light.infor_main_color}
        />
      </View>

      <FlatList
        data={products}
        renderItem={renderItem}
        keyExtractor={item => item.Id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        snapToInterval={ITEM_WIDTH + ITEM_SPACING}
        snapToAlignment="start"
        decelerationRate="fast"
        pagingEnabled={false}
        disableIntervalMomentum={true}
        ItemSeparatorComponent={ItemSeparator}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    ...TypoSkin.subtitle1,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_title_color,
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAllText: {
    ...TypoSkin.regular1,
    color: ColorThemes.light.infor_main_color,
    marginRight: 4,
  },
  listContent: {
    paddingVertical: 8,
    paddingRight: 16,
  },
});

export default HotProductsRow;
