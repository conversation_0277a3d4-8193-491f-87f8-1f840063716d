import React from 'react';
import {View, Text, StyleSheet} from 'react-native';

interface SectionProps {
  title: string;
  children: React.ReactNode;
}

const Section = ({title, children}: SectionProps) => (
  <View style={styles.sectionContainer}>
    <Text style={styles.sectionTitle}>{title}</Text>
    {children}
  </View>
);

const styles = StyleSheet.create({
  sectionContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
});

export default Section;
