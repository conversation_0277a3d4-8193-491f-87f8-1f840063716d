import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { AppSvg, TextField, Winicon } from 'wini-mobile-components';
import { RootScreen } from '../../router/router';
import { Title } from '../../Config/Contanst';
import { ScrollView } from 'react-native-gesture-handler';
import { Controller, useForm } from 'react-hook-form';
import { TextInput } from 'react-native-paper';
import { LeaderShopInfoData } from '../dto/dto';
import { TypoSkin } from '../../assets/skin/typography';
import { ColorThemes } from '../../assets/skin/colors';
import iconSvg from '../../svg/icon';

interface LeaderShopInfoProps {
  shop: any[];
}

const LeaderShopInfo = (props: LeaderShopInfoProps) => {
  const { shop } = props;
  const navigation = useNavigation<any>();
  const [stateEdit, setStateEdit] = useState<LeaderShopInfoData>({
    Id: '',
    DateCreated: 0,
    name: '',
    Phone: '',
    Email: '',
    description: '',
    Status: 0,
    Address: '',
  });
  const handleNavigateOrders = (
    order: string,
    dataEdit: LeaderShopInfoData,
    status?: string,
  ) => {
    navigation.push(order, { data: dataEdit, status: status });
  };
  useEffect(() => {
    if (shop && shop.length > 0) {
      setStateEdit({
        name: shop[0]?.Name,
        Phone: shop[0]?.Mobile,
        Email: shop[0]?.Email,
        description: shop[0]?.Description,
        Status: shop[0]?.Status,
        Id: shop[0]?.Id,
        DateCreated: shop[0]?.DateCreated,
        Address: shop[0]?.Address,
      });
    }
  }, [shop]);
  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <View style={styles.cardHeader}>
          <View style={styles.cardHeaderLeft}>
            <View style={{marginTop: 10, marginBottom: 10}}>
              <AppSvg SvgSrc={iconSvg.editShopInfo} size={24} />
            </View>
            <Text style={styles.cardTitle}>Thông tin chi tiết cửa hàng</Text>
          </View>
          <TouchableOpacity
            style={styles.cardHeaderRight}
            onPress={() =>
              handleNavigateOrders(RootScreen.RegisterShop, stateEdit, 'edit')
            }>
            <AppSvg SvgSrc={iconSvg.editShopInfo2} size={20} />
          </TouchableOpacity>
        </View>
        <View style={styles.cardContent}>
          <Text style={styles.infoValue}>
            Họ và tên: <Text style={styles.infoValue}>{stateEdit.name}</Text>
          </Text>
          <Text style={styles.infoValue}>
            SDT: <Text style={styles.infoValue}>{stateEdit.Phone}</Text>
          </Text>
          <Text style={styles.infoValue}>
            Email: <Text style={styles.infoValue}>{stateEdit.Email}</Text>
          </Text>
          {/* <Text style={styles.infoValue}>Ngành nghề: <Text style={styles.infoValue}>{stateEdit.BusinessType}</Text></Text> */}
          <Text style={styles.infoValue}>
            Mô tả: <Text style={styles.infoValue}>{stateEdit.description}</Text>
          </Text>
          <Text style={styles.infoValue}>
            Trạng thái:{' '}
            <Text style={styles.infoValue}>
              {stateEdit.Status == 0 ? 'Khởi tạo' : 'Hoạt động'}
            </Text>
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    marginTop: 10,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#fff',
  },
  navItem: {
    alignItems: 'center',
    borderColor: '#00FFFF',
    borderWidth: 0.3,
    padding: 10,
    borderRadius: 10,
    maxWidth: 67,
    maxHeight: 68,
  },
  navText: {
    fontSize: 10,
    color: '#333',
    marginTop: 4,
    textAlign: 'center',
  },
  navBadge: {
    position: 'absolute',
    top: 5,
    right: 4,
    backgroundColor: '#FF0000',
    color: '#fff',
    fontSize: 10,
    paddingHorizontal: 4,
    borderRadius: 10,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 20,
    shadowColor: '#1890FF4D',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    paddingHorizontal: 4,
    borderWidth: 0.3,
    borderColor: '#1890FF4D',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    maxHeight: 74,
    marginLeft: 20,
    marginRight: 7,
    borderBottomWidth: 0.5,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  cardTitle: {
    ...TypoSkin.title5,
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  cardHeaderLeft: {
    display: 'flex',
    flexDirection: 'row',
    alignContent: 'center',
    gap: 10,
    flex: 1,
  },
  cardHeaderRight: {},

  cardContent: {
    gap: 6,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  infoLabel: {
    ...TypoSkin.title4,
    color: '#666',
    marginBottom: 5,
  },
  infoValue: {
    ...TypoSkin.title4,
    fontSize: 14,
    color: '#000',
    fontWeight: '400',
    lineHeight: 22,
  },
  infoValueEdit: {
    ...TypoSkin.body3,
    display: 'flex',
    alignItems: 'center',
    color: '#000',
    fontWeight: '400',
  },
});

export default LeaderShopInfo;
