import React from 'react';
import {TouchableOpacity, Text} from 'react-native';
import {FormatIconProps} from './types';
import {styles} from './styles';

const FormatIcon: React.FC<FormatIconProps> = ({label, active, onPress}) => (
  <TouchableOpacity
    onPress={onPress}
    style={[styles.iconButton, active && styles.iconActive]}>
    <Text
      style={[
        styles.iconText,
        {
          fontWeight: label === 'B' ? 'bold' : 'normal',
          fontStyle: label === 'I' ? 'italic' : 'normal',
          textDecorationLine: label === 'U' ? 'underline' : 'none',
          color: active ? '#000' : '#666',
        },
      ]}>
      {label}
    </Text>
  </TouchableOpacity>
);

export default FormatIcon;
