import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {Dispatch} from 'redux';
import {DataController} from '../../../base/baseController';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {StorageContanst} from '../../../Config/Contanst';
import {randomGID} from '../../../utils/Utils';
import {newsFeedActions} from './newsFeedReducer';
import store from '../../../redux/store/store';
import {SocialDA} from '../da';

interface CommentState {
  byPostId: {
    [postId: string]: {
      data: any[];
      loading: boolean;
      error: any;
      hasMore: boolean;
      page: number;
    };
  };
}

const initialState: CommentState = {
  byPostId: {},
};

export const postCommentsSlice = createSlice({
  name: 'postComments',
  initialState,
  reducers: {
    fetchCommentsStart: (state, action: PayloadAction<string>) => {
      const postId = action.payload;
      // Ensure the postId entry exists before updating
      if (!state.byPostId[postId]) {
        state.byPostId[postId] = {
          data: [],
          loading: false,
          error: null,
          hasMore: false,
          page: 1,
        };
      }
      state.byPostId[postId] = {
        ...state.byPostId[postId],
        loading: true,
        error: null,
      };
    },
    fetchCommentsSuccess: (
      state,
      action: PayloadAction<{
        postId: string;
        comments: any[];
        hasMore: boolean;
      }>,
    ) => {
      const {postId, comments, hasMore} = action.payload;
      state.byPostId[postId] = {
        data: comments,
        loading: false,
        error: null,
        hasMore,
        page: 1,
      };
    },
    addComment: (
      state,
      action: PayloadAction<{postId: string; comment: any}>,
    ) => {
      const {postId, comment} = action.payload;
      // Ensure the postId entry exists before adding comment
      if (!state.byPostId[postId]) {
        state.byPostId[postId] = {
          data: [],
          loading: false,
          error: null,
          hasMore: false,
          page: 1,
        };
      }
      state.byPostId[postId].data.push(comment);
    },
    updateCommentLike: (
      state,
      action: PayloadAction<{
        postId: string;
        commentId: string;
        isLike: boolean;
      }>,
    ) => {
      const {postId, commentId, isLike} = action.payload;
      // Ensure the postId entry exists before updating like
      if (!state.byPostId[postId]) {
        state.byPostId[postId] = {
          data: [],
          loading: false,
          error: null,
          hasMore: false,
          page: 1,
        };
      }

      const comments = state.byPostId[postId].data;
      const comment = comments.find(c => c.Id === commentId);
      if (comment) {
        comment.Likes = isLike
          ? (comment.Likes ?? 0) + 1
          : Math.max(0, (comment.Likes ?? 0) - 1); // Prevent negative likes
        comment.IsLike = isLike;
      }
    },
  },
});

const socialDA = new SocialDA();

export const postCommentsActions = {
  loadComments:
    (postId: string, isNew?: boolean, isEvent?: boolean) =>
    async (dispatch: Dispatch) => {
      try {
        dispatch(postCommentsSlice.actions.fetchCommentsStart(postId));
        const comments = await socialDA.getComment(postId, isNew, isEvent);
        if (comments?.code !== 200) {
          return;
        }
        const cmts = comments?.data.map((item: any) => {
          if (comments.Customer.length > 0) {
            const userCmt = comments.Customer.find(
              (cus: any) => cus.Id === item.CustomerId,
            );
            return {
              ...item,
              relativeUser: {
                image: userCmt?.AvatarUrl,
                title: userCmt?.Name || 'Người dùng Chanivo',
                subtitle: item.DateCreated,
              },
            };
          }
          return {
            ...item,
            relativeUser: {
              image: undefined,
              title: 'Người dùng Chanivo',
              subtitle: item.DateCreated,
            },
          };
        });
        dispatch(
          postCommentsSlice.actions.fetchCommentsSuccess({
            postId,
            comments: cmts || [],
            hasMore: false,
          }),
        );
      } catch (error) {
        console.error('Error loading comments:', error);
      }
    },

  addNewComment:
    (
      postId: string,
      Content: string,
      commentId?: string,
      isNew?: boolean,
      isEvent?: boolean,
    ) =>
    async (dispatch: Dispatch) => {
      const result = await socialDA.addComment(
        postId,
        Content,
        commentId,
        isNew,
        isEvent,
      );
      const userCmt = store.getState().customer.data;
      if (result) {
        // Dispatch action để thêm comment mới
        if (isNew || isEvent) {
          return;
        }
        dispatch(
          postCommentsSlice.actions.addComment({
            postId,
            comment: {
              ...result,
              relativeUser: {
                image: userCmt?.AvatarUrl,
                title: userCmt?.Name || 'Người dùng Chanivo',
                subtitle: result.DateCreated,
              },
            },
          }),
        );
      }
    },

  toggleLike:
    (postId: string, commentId: string, isLike: boolean) =>
    async (dispatch: Dispatch) => {
      const likeController = new DataController('Like');
      var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);

      if (cusId) {
        if (isLike) {
          // Unlike - Delete existing like
          const result = await likeController.getListSimple({
            query: `@CustomerId: {${cusId}} @CommentId:{${commentId}}`,
          });
          if (result.data?.length > 0) {
            const unlike = await likeController.delete([result.data[0].Id]);
            if (unlike.code === 200) {
              dispatch(
                postCommentsSlice.actions.updateCommentLike({
                  postId,
                  commentId,
                  isLike: false,
                }),
              );
            }
          }
        } else {
          // Like - Add new like
          const data = {
            Id: randomGID(),
            CustomerId: cusId,
            CommentId: commentId,
            DateCreated: new Date().getTime(),
          };
          const result = await likeController.add([data]);
          if (result.code === 200) {
            dispatch(
              postCommentsSlice.actions.updateCommentLike({
                postId,
                commentId,
                isLike: true,
              }),
            );
          }
        }
      }
    },
};

export default postCommentsSlice.reducer;
