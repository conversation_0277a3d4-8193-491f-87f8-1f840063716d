import React, { forwardRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Title } from '../../Config/Contanst';
import ShopPromortionComponent from '../../components/shop/ShopPromotion/ShopPromortion';
import ScreenHeader from '../../Screen/Layout/header';
import { InforHeader } from '../../Screen/Layout/headers/inforHeader';

const ShopPromortion = () => {
  return (
    <View style={styles.container}>
      <InforHeader title={Title.ManagePromorion} showAction />
      <ShopPromortionComponent />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
});

export default ShopPromortion;
