import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import SocketDebugger from '../../../utils/SocketDebugger';
import { useSocketStatus } from '../../../hooks/useSocketConnection';

interface SocketDebugInfoProps {
  visible?: boolean;
}

const SocketDebugInfo: React.FC<SocketDebugInfoProps> = ({ visible = false }) => {
  const [connectionCount, setConnectionCount] = useState(0);
  const [showDetails, setShowDetails] = useState(false);
  const { isConnected, connectionStatus } = useSocketStatus();

  useEffect(() => {
    const interval = setInterval(() => {
      setConnectionCount(SocketDebugger.getConnectionCount());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (!visible) return null;

  const hasMultipleConnections = connectionCount > 1;

  return (
    <View style={[styles.container, hasMultipleConnections && styles.warningContainer]}>
      <TouchableOpacity 
        style={styles.header}
        onPress={() => setShowDetails(!showDetails)}
      >
        <Text style={[styles.title, hasMultipleConnections && styles.warningText]}>
          🔌 Socket Debug {hasMultipleConnections ? '⚠️' : '✅'}
        </Text>
        <Text style={styles.arrow}>{showDetails ? '▼' : '▶'}</Text>
      </TouchableOpacity>

      {showDetails && (
        <View style={styles.details}>
          <Text style={styles.detailText}>
            Status: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
          </Text>
          <Text style={styles.detailText}>
            Connection Status: {connectionStatus}
          </Text>
          <Text style={[
            styles.detailText,
            hasMultipleConnections && styles.warningText
          ]}>
            Active Connections: {connectionCount}
          </Text>
          
          {hasMultipleConnections && (
            <Text style={styles.warningMessage}>
              ⚠️ Multiple socket connections detected! This may cause issues.
            </Text>
          )}

          <TouchableOpacity 
            style={styles.button}
            onPress={() => SocketDebugger.logAllConnections()}
          >
            <Text style={styles.buttonText}>Log All Connections</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.button, styles.clearButton]}
            onPress={() => SocketDebugger.clear()}
          >
            <Text style={styles.buttonText}>Clear Debug Data</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.BgColor,
    borderWidth: 1,
    borderColor: ColorThemes.BorderColor,
    borderRadius: 8,
    margin: 10,
    overflow: 'hidden',
  },
  warningContainer: {
    borderColor: '#ff6b6b',
    backgroundColor: '#fff5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: ColorThemes.Primary,
  },
  title: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  warningText: {
    color: '#ff6b6b',
  },
  arrow: {
    color: 'white',
    fontSize: 12,
  },
  details: {
    padding: 12,
  },
  detailText: {
    fontSize: 12,
    marginBottom: 4,
    color: ColorThemes.TextColor,
  },
  warningMessage: {
    color: '#ff6b6b',
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 8,
  },
  button: {
    backgroundColor: ColorThemes.Primary,
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
    alignItems: 'center',
  },
  clearButton: {
    backgroundColor: '#ff6b6b',
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default SocketDebugInfo;
