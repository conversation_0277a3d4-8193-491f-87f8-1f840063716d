# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# WebRTC ProGuard rules
-keep class org.webrtc.** { *; }
-dontwarn org.webrtc.**
-keep class com.oney.WebRTCModule.** { *; }
-dontwarn com.oney.WebRTCModule.**

# Keep WebRTC native methods
-keepclassmembers class org.webrtc.** {
    native <methods>;
}

# Keep WebRTC enums
-keepclassmembers enum org.webrtc.** {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
