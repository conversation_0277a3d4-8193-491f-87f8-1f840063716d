import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {NewsItem} from '../../../redux/models/news';
import {formatTimestampToDate} from '../../../utils/Utils';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';

interface NewsCardProps {
  item: NewsItem;
  onPress: () => void;
}

const NewsCard: React.FC<NewsCardProps> = ({item, onPress}) => {
  const displayType = 'Tin tức';

  return (
    <TouchableOpacity style={styles.cardContainer} onPress={onPress}>
      <Image source={{uri: item.Img}} style={styles.cardImage} />
      <View style={styles.cardTextContainer}>
        <Text style={styles.cardTitle} numberOfLines={2}>
          {item.Name}
        </Text>
        <Text style={[styles.cardType]}>{displayType}</Text>
        <Text style={styles.cardTimestamp}>
          {formatTimestampToDate(item.DateCreated)} {/* Sử dụng hàm format */}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    width: 160,
    marginRight: 12,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: 100,
  },
  cardTextContainer: {
    padding: 10,
  },
  cardTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#1c1e21',
    marginBottom: 4,
    height: 44,
  },
  cardType: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.infor_main_color,
    alignSelf: 'flex-start',
  },
  cardTimestamp: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default NewsCard;
