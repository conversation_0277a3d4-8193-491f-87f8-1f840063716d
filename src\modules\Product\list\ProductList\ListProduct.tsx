import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  RefreshControl,
  Text,
  View,
} from 'react-native';
import {RootState} from '../../../../redux/store/store';
import {useDispatch, useSelector} from 'react-redux';
import {useCallback, useEffect, useState} from 'react';
import {Product} from '../../../../redux/models/product';
import ProductCard from '../../card/ProductCard';
import React from 'react';
import {ColorThemes} from '../../../../assets/skin/colors';
import EmptyPage from '../../../../Screen/emptyPage';
import {
  fetchProducts,
  updateFavoriteProduct,
} from '../../../../redux/actions/productAction';
import {RootScreen} from '../../../../router/router';
import {useNavigation} from '@react-navigation/native';
import {brandAction} from '../../../../redux/actions/brandAction';
import {CartActions} from '../../../../redux/reducers/CartReducer';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import DefaultBanner from '../../../banner/Banner';

const {width} = Dimensions.get('window');

const ListProduct = () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const {data, onLoading} = useSelector((state: RootState) => state.product);
  const {filter} = useSelector((state: RootState) => state.productByCategory);

  // States cho load more
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchProduct = useCallback(
    async (pageNumber: number = 1, isLoadMore: boolean = false) => {
      if (isLoadMore) {
        setIsLoadingMore(true);
      }

      const config: any = {
        page: pageNumber,
        size: 10,
        searchRaw: '',
        sortby: [],
        query: '',
      };
      if (filter.categoryId)
        config.searchRaw += `@CategoryId:{${filter.categoryId}}`;

      if (filter.activeFilters.IsHot) config.searchRaw += ` @IsHot:{true}`;
      if (filter.activeFilters.IsFreeShip)
        config.searchRaw += ` @IsFreeShip:{true}`;

      if (filter.maxPrice) {
        config.searchRaw += `@Price:[0 ${filter.maxPrice}] `;
      }

      if (filter.textSearch && filter.textSearch.length)
        config.searchRaw += `(@Name:*${filter.textSearch}*) `;

      if (filter.activeFilters.IsNew)
        config.sortby = [{prop: 'DateCreated', direction: 'DESC'}];

      if (filter.brandId) config.searchRaw += ` @BrandId:{${filter.brandId}}`;

      if (filter.activeFilters.FavoriteBrand) {
        const brands = await brandAction.fetch({
          searchRaw: `@IsFavorite:{true}`,
        });
        if (brands.length)
          config.searchRaw += ` @BrandId:{${brands
            .map((brand: any) => brand.Id)
            .join(' | ')}}`;
      }

      if (filter.sortOption) {
        switch (filter.sortOption) {
          case 'newest':
            config.sortby = [{prop: 'DateCreated', direction: 'DESC'}];
            break;
          case 'price_asc':
            config.sortby = [{prop: 'Price', direction: 'ASC'}];
            break;
          case 'price_desc':
            config.sortby = [{prop: 'Price', direction: 'DESC'}];
            break;
          default:
            break;
        }
      }

      if (!config.searchRaw?.length) delete config.searchRaw;
      if (!config.sortby?.length) delete config.sortby;
      if (!config.query?.length) delete config.query;

      // Thêm flag để biết là load more hay không
      try {
        const result = await dispatch(
          fetchProducts({...config, isLoadMore}) as any,
        );

        // Kiểm tra xem còn data để load không
        if (result.meta.requestStatus === 'fulfilled' && result.payload) {
          if (result.payload.data.length < 10) {
            setHasMoreData(false);
          }
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setIsLoadingMore(false);
        setIsRefreshing(false);
      }
    },
    [filter, dispatch],
  );

  // effect get sản phẩm - reset về page 1 khi filter thay đổi
  useEffect(() => {
    setPage(1);
    setHasMoreData(true);
    fetchProduct(1, false);
  }, [filter]);

  // xem chi tiết sản phẩm
  const handleProductPress = useCallback((product: Product) => {
    navigation.navigate(RootScreen.ProductDetail, {id: product.Id});
  }, []);

  const handleFavoritePress = (item: Product) => {
    dispatch(
      updateFavoriteProduct({...item, IsFavorite: !item.IsFavorite}) as any,
    );
  };

  const onAddToCart = (item: Product) => {
    CartActions.addItemToCart(item, 1)(dispatch);
    showSnackbar({
      message: 'Đã thêm sản phẩm vào giỏ hàng',
      status: ComponentStatus.SUCCSESS,
    });
  };

  // render item sản phẩm
  const renderProductItem = useCallback(
    ({item}: {item: Product}) => (
      <View style={{marginHorizontal: 8}}>
        <ProductCard
          item={item}
          onPress={handleProductPress}
          onAddToCart={onAddToCart}
          onFavoritePress={handleFavoritePress}
          width={(width - 48) / 2}
          height={((width - 48) / 2) * 1.8}
        />
      </View>
    ),
    [],
  );

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && hasMoreData && !onLoading) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchProduct(nextPage, true);
    }
  }, [isLoadingMore, hasMoreData, onLoading, page, fetchProduct]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    setPage(1);
    setHasMoreData(true);
    fetchProduct(1, false);
  }, [fetchProduct]);

  // Render footer loading
  const renderFooter = useCallback(() => {
    if (!isLoadingMore) return null;

    return (
      <View style={{paddingVertical: 20}}>
        <ActivityIndicator
          size="small"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }, [isLoadingMore]);

  // hiển thị loading lần đầu
  if (onLoading && page === 1) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  // hiển thị trang trống
  if (data.length === 0 && !onLoading) {
    return <EmptyPage />;
  }

  return (
    <FlatList
      data={data}
      renderItem={renderProductItem}
      keyExtractor={(item, index) => item.Id || index.toString()}
      numColumns={2}
      contentContainerStyle={{paddingHorizontal: 8, paddingBottom: 16}}
      ListHeaderComponent={() => (
        <>
          <View style={{marginHorizontal: 8, marginTop: 8}}>
            <DefaultBanner type={4} position={1} />
          </View>
          <Text
            style={{
              fontSize: 16,
              fontWeight: 'bold',
              marginLeft: 8,
              marginTop: 16,
              marginBottom: 12,
            }}>
            Cửa hàng gợi ý
          </Text>
        </>
      )}
      ListFooterComponent={renderFooter}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
          colors={[ColorThemes.light.primary_main_color]}
          tintColor={ColorThemes.light.primary_main_color}
        />
      }
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      showsVerticalScrollIndicator={false}
      removeClippedSubviews={true} // Tối ưu performance
      maxToRenderPerBatch={10} // Giới hạn số item render mỗi batch
      windowSize={10} // Tối ưu memory
    />
  );
};

export default ListProduct;
